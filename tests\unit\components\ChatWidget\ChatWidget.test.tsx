/**
 * ChatWidget Component Unit Tests
 * 
 * Tests for chat widget functionality, configuration, and user interactions.
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ChatWidget from '../../../../src/components/ChatWidget/ChatWidget';

// Mock WebSocket
const mockWebSocket = {
  send: vi.fn(),
  close: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  readyState: WebSocket.OPEN,
};

global.WebSocket = vi.fn(() => mockWebSocket) as any;

// Mock API service
vi.mock('../../../../src/services/api', () => ({
  WebtonAPIClient: vi.fn(() => ({
    sendMessage: vi.fn().mockResolvedValue({
      id: 'msg-123',
      content: 'Hello! How can I help you?',
      timestamp: new Date().toISOString(),
      isBot: true,
    }),
  })),
}));

describe('ChatWidget', () => {
  const defaultProps = {
    companyName: 'Test Company',
    logo: 'https://example.com/logo.png',
    primaryColor: '#4f46e5',
    secondaryColor: '#f3f4f6',
    greetingMessage: 'Hello! How can I help you today?',
    position: 'bottom-right' as const,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render widget button with correct styling', () => {
    render(<ChatWidget {...defaultProps} />);

    const widgetButton = screen.getByRole('button', { name: /open chat/i });
    expect(widgetButton).toBeInTheDocument();
    expect(widgetButton).toHaveStyle({
      backgroundColor: defaultProps.primaryColor,
    });
  });

  it('should position widget correctly', () => {
    const { rerender } = render(<ChatWidget {...defaultProps} position="bottom-right" />);
    
    let widget = screen.getByTestId('chat-widget');
    expect(widget).toHaveClass('bottom-4', 'right-4');

    rerender(<ChatWidget {...defaultProps} position="bottom-left" />);
    
    widget = screen.getByTestId('chat-widget');
    expect(widget).toHaveClass('bottom-4', 'left-4');
  });

  it('should toggle chat window when button is clicked', async () => {
    const user = userEvent.setup();
    render(<ChatWidget {...defaultProps} />);

    const widgetButton = screen.getByRole('button', { name: /open chat/i });
    
    // Initially closed
    expect(screen.queryByTestId('chat-window')).not.toBeInTheDocument();

    // Click to open
    await user.click(widgetButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('chat-window')).toBeInTheDocument();
    });

    // Click to close
    const closeButton = screen.getByRole('button', { name: /close chat/i });
    await user.click(closeButton);

    await waitFor(() => {
      expect(screen.queryByTestId('chat-window')).not.toBeInTheDocument();
    });
  });

  it('should display greeting message when chat opens', async () => {
    const user = userEvent.setup();
    render(<ChatWidget {...defaultProps} />);

    const widgetButton = screen.getByRole('button', { name: /open chat/i });
    await user.click(widgetButton);

    await waitFor(() => {
      expect(screen.getByText(defaultProps.greetingMessage)).toBeInTheDocument();
    });
  });

  it('should display company name and logo', async () => {
    const user = userEvent.setup();
    render(<ChatWidget {...defaultProps} />);

    const widgetButton = screen.getByRole('button', { name: /open chat/i });
    await user.click(widgetButton);

    await waitFor(() => {
      expect(screen.getByText(defaultProps.companyName)).toBeInTheDocument();
      expect(screen.getByAltText(`${defaultProps.companyName} logo`)).toBeInTheDocument();
    });
  });

  it('should send and receive messages', async () => {
    const user = userEvent.setup();
    render(<ChatWidget {...defaultProps} />);

    // Open chat
    const widgetButton = screen.getByRole('button', { name: /open chat/i });
    await user.click(widgetButton);

    await waitFor(() => {
      expect(screen.getByTestId('chat-window')).toBeInTheDocument();
    });

    // Type and send message
    const messageInput = screen.getByPlaceholderText(/type your message/i);
    await user.type(messageInput, 'Hello, I need help');
    
    const sendButton = screen.getByRole('button', { name: /send/i });
    await user.click(sendButton);

    // Should show user message
    await waitFor(() => {
      expect(screen.getByText('Hello, I need help')).toBeInTheDocument();
    });

    // Should show bot response
    await waitFor(() => {
      expect(screen.getByText('Hello! How can I help you?')).toBeInTheDocument();
    });

    // Input should be cleared
    expect(messageInput).toHaveValue('');
  });

  it('should handle message sending with Enter key', async () => {
    const user = userEvent.setup();
    render(<ChatWidget {...defaultProps} />);

    // Open chat
    const widgetButton = screen.getByRole('button', { name: /open chat/i });
    await user.click(widgetButton);

    await waitFor(() => {
      expect(screen.getByTestId('chat-window')).toBeInTheDocument();
    });

    // Type message and press Enter
    const messageInput = screen.getByPlaceholderText(/type your message/i);
    await user.type(messageInput, 'Test message{enter}');

    // Should send message
    await waitFor(() => {
      expect(screen.getByText('Test message')).toBeInTheDocument();
    });
  });

  it('should prevent sending empty messages', async () => {
    const user = userEvent.setup();
    render(<ChatWidget {...defaultProps} />);

    // Open chat
    const widgetButton = screen.getByRole('button', { name: /open chat/i });
    await user.click(widgetButton);

    await waitFor(() => {
      expect(screen.getByTestId('chat-window')).toBeInTheDocument();
    });

    // Try to send empty message
    const sendButton = screen.getByRole('button', { name: /send/i });
    await user.click(sendButton);

    // Should not send message
    expect(screen.queryByText('')).not.toBeInTheDocument();
  });

  it('should show typing indicator when bot is responding', async () => {
    const user = userEvent.setup();
    
    // Mock delayed response
    const mockSendMessage = vi.fn(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({
          id: 'msg-123',
          content: 'Response',
          timestamp: new Date().toISOString(),
          isBot: true,
        }), 1000)
      )
    );

    vi.mocked(require('../../../../src/services/api').WebtonAPIClient).mockImplementation(() => ({
      sendMessage: mockSendMessage,
    }));

    render(<ChatWidget {...defaultProps} />);

    // Open chat and send message
    const widgetButton = screen.getByRole('button', { name: /open chat/i });
    await user.click(widgetButton);

    const messageInput = screen.getByPlaceholderText(/type your message/i);
    await user.type(messageInput, 'Test message');
    
    const sendButton = screen.getByRole('button', { name: /send/i });
    await user.click(sendButton);

    // Should show typing indicator
    await waitFor(() => {
      expect(screen.getByTestId('typing-indicator')).toBeInTheDocument();
    });
  });

  it('should handle connection errors gracefully', async () => {
    const user = userEvent.setup();
    
    // Mock API error
    const mockSendMessage = vi.fn().mockRejectedValue(new Error('Connection failed'));
    
    vi.mocked(require('../../../../src/services/api').WebtonAPIClient).mockImplementation(() => ({
      sendMessage: mockSendMessage,
    }));

    render(<ChatWidget {...defaultProps} />);

    // Open chat and send message
    const widgetButton = screen.getByRole('button', { name: /open chat/i });
    await user.click(widgetButton);

    const messageInput = screen.getByPlaceholderText(/type your message/i);
    await user.type(messageInput, 'Test message');
    
    const sendButton = screen.getByRole('button', { name: /send/i });
    await user.click(sendButton);

    // Should show error message
    await waitFor(() => {
      expect(screen.getByText(/failed to send message/i)).toBeInTheDocument();
    });
  });

  it('should apply custom theme colors', () => {
    const customProps = {
      ...defaultProps,
      primaryColor: '#ff0000',
      secondaryColor: '#00ff00',
    };

    render(<ChatWidget {...customProps} />);

    const widgetButton = screen.getByRole('button', { name: /open chat/i });
    expect(widgetButton).toHaveStyle({
      backgroundColor: '#ff0000',
    });
  });

  it('should handle controlled open state', async () => {
    const mockOnToggle = vi.fn();
    const { rerender } = render(
      <ChatWidget {...defaultProps} isOpen={false} onToggle={mockOnToggle} />
    );

    // Should be closed initially
    expect(screen.queryByTestId('chat-window')).not.toBeInTheDocument();

    // Rerender with open state
    rerender(<ChatWidget {...defaultProps} isOpen={true} onToggle={mockOnToggle} />);

    // Should be open
    expect(screen.getByTestId('chat-window')).toBeInTheDocument();
  });

  it('should scroll to bottom when new messages arrive', async () => {
    const user = userEvent.setup();
    
    // Mock scrollIntoView
    const mockScrollIntoView = vi.fn();
    Element.prototype.scrollIntoView = mockScrollIntoView;

    render(<ChatWidget {...defaultProps} />);

    // Open chat and send message
    const widgetButton = screen.getByRole('button', { name: /open chat/i });
    await user.click(widgetButton);

    const messageInput = screen.getByPlaceholderText(/type your message/i);
    await user.type(messageInput, 'Test message');
    
    const sendButton = screen.getByRole('button', { name: /send/i });
    await user.click(sendButton);

    // Should scroll to bottom
    await waitFor(() => {
      expect(mockScrollIntoView).toHaveBeenCalled();
    });
  });
});

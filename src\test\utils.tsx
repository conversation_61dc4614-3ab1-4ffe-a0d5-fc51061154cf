/**
 * Test Utilities
 * 
 * Common utilities and helpers for testing components and functionality.
 */

import React from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { AuthProvider } from '../contexts/AuthContext';
import { vi } from 'vitest';

// Mock user profiles for testing
export const mockUsers = {
  admin: {
    id: 'admin-123',
    email: '<EMAIL>',
    full_name: 'Admin User',
    role: 'admin',
    company_name: 'Test Company',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  },
  client: {
    id: 'client-123',
    email: '<EMAIL>',
    full_name: 'Client User',
    role: 'client',
    company_name: 'Client Company',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  },
  viewer: {
    id: 'viewer-123',
    email: '<EMAIL>',
    full_name: 'Viewer User',
    role: 'viewer',
    company_name: 'Viewer Company',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  },
};

// Mock authentication context
export const createMockAuthContext = (user: typeof mockUsers.admin | null = null) => ({
  user: user ? { id: user.id, email: user.email } : null,
  session: user ? { user: { id: user.id, email: user.email } } : null,
  profile: user,
  loading: false,
  signIn: vi.fn().mockResolvedValue({ error: null }),
  signUp: vi.fn().mockResolvedValue({ error: null }),
  signOut: vi.fn().mockResolvedValue(undefined),
  resetPassword: vi.fn().mockResolvedValue({ error: null }),
  updateProfile: vi.fn().mockResolvedValue({ error: null }),
  canManageUsers: user?.role === 'admin',
  canManageBots: ['admin', 'client'].includes(user?.role || ''),
  canViewAnalytics: ['admin', 'client', 'viewer'].includes(user?.role || ''),
  canEditSettings: ['admin', 'client'].includes(user?.role || ''),
  canExportData: ['admin', 'client'].includes(user?.role || ''),
});

// Test wrapper with providers
interface TestWrapperProps {
  children: React.ReactNode;
  authContext?: ReturnType<typeof createMockAuthContext>;
  initialRoute?: string;
}

const TestWrapper: React.FC<TestWrapperProps> = ({ 
  children, 
  authContext = createMockAuthContext(),
  initialRoute = '/'
}) => {
  // Mock AuthProvider
  const MockAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const AuthContext = React.createContext(authContext);
    return <AuthContext.Provider value={authContext}>{children}</AuthContext.Provider>;
  };

  return (
    <BrowserRouter>
      <MockAuthProvider>
        {children}
      </MockAuthProvider>
    </BrowserRouter>
  );
};

// Custom render function with providers
export const renderWithProviders = (
  ui: React.ReactElement,
  options: RenderOptions & {
    authContext?: ReturnType<typeof createMockAuthContext>;
    initialRoute?: string;
  } = {}
) => {
  const { authContext, initialRoute, ...renderOptions } = options;

  return render(ui, {
    wrapper: ({ children }) => (
      <TestWrapper authContext={authContext} initialRoute={initialRoute}>
        {children}
      </TestWrapper>
    ),
    ...renderOptions,
  });
};

// Mock file creation helper
export const createMockFile = (
  name: string,
  content: string = 'mock file content',
  type: string = 'text/plain'
): File => {
  const file = new File([content], name, { type });
  return file;
};

// Mock API responses
export const mockAPIResponses = {
  login: {
    success: true,
    data: {
      user: { id: '123', email: '<EMAIL>' },
      token: 'mock-jwt-token',
      refreshToken: 'mock-refresh-token',
    },
  },
  
  bots: {
    success: true,
    data: [
      {
        id: 'bot-1',
        name: 'Test Bot 1',
        description: 'First test bot',
        isActive: true,
        settings: {
          primaryColor: '#4f46e5',
          greetingMessage: 'Hello!',
        },
      },
      {
        id: 'bot-2',
        name: 'Test Bot 2',
        description: 'Second test bot',
        isActive: false,
        settings: {
          primaryColor: '#059669',
          greetingMessage: 'Hi there!',
        },
      },
    ],
  },

  documents: {
    success: true,
    data: [
      {
        id: 'doc-1',
        title: 'Test Document 1',
        description: 'First test document',
        processingStatus: 'completed',
        uploadedAt: '2023-01-01T00:00:00Z',
        fileSize: 1024,
        fileType: 'application/pdf',
      },
      {
        id: 'doc-2',
        title: 'Test Document 2',
        description: 'Second test document',
        processingStatus: 'processing',
        uploadedAt: '2023-01-02T00:00:00Z',
        fileSize: 2048,
        fileType: 'text/plain',
      },
    ],
  },

  analytics: {
    success: true,
    data: {
      totalConversations: 150,
      totalMessages: 1250,
      averageResponseTime: 2.5,
      userSatisfaction: 4.2,
      conversationsByDay: [
        { date: '2023-01-01', count: 10 },
        { date: '2023-01-02', count: 15 },
        { date: '2023-01-03', count: 12 },
      ],
      messagesByHour: [
        { hour: 9, count: 25 },
        { hour: 10, count: 30 },
        { hour: 11, count: 35 },
      ],
    },
  },
};

// Mock fetch helper
export const mockFetch = (response: any, status: number = 200, delay: number = 0) => {
  return vi.fn().mockImplementation(() =>
    new Promise((resolve) =>
      setTimeout(() => {
        resolve({
          ok: status >= 200 && status < 300,
          status,
          headers: new Headers({ 'content-type': 'application/json' }),
          json: async () => response,
          text: async () => JSON.stringify(response),
        });
      }, delay)
    )
  );
};

// Wait for async operations
export const waitForAsync = (ms: number = 0) =>
  new Promise((resolve) => setTimeout(resolve, ms));

// Mock WebSocket for testing
export class MockWebSocket {
  static CONNECTING = 0;
  static OPEN = 1;
  static CLOSING = 2;
  static CLOSED = 3;

  readyState = MockWebSocket.CONNECTING;
  onopen: ((event: Event) => void) | null = null;
  onclose: ((event: CloseEvent) => void) | null = null;
  onmessage: ((event: MessageEvent) => void) | null = null;
  onerror: ((event: Event) => void) | null = null;

  constructor(public url: string) {
    setTimeout(() => {
      this.readyState = MockWebSocket.OPEN;
      if (this.onopen) {
        this.onopen(new Event('open'));
      }
    }, 0);
  }

  send(data: string) {
    if (this.readyState !== MockWebSocket.OPEN) {
      throw new Error('WebSocket is not open');
    }
    // Echo back for testing
    setTimeout(() => {
      if (this.onmessage) {
        this.onmessage(new MessageEvent('message', { data }));
      }
    }, 10);
  }

  close() {
    this.readyState = MockWebSocket.CLOSED;
    if (this.onclose) {
      this.onclose(new CloseEvent('close'));
    }
  }

  addEventListener(type: string, listener: EventListener) {
    if (type === 'open') this.onopen = listener as any;
    if (type === 'close') this.onclose = listener as any;
    if (type === 'message') this.onmessage = listener as any;
    if (type === 'error') this.onerror = listener as any;
  }

  removeEventListener(type: string, listener: EventListener) {
    if (type === 'open') this.onopen = null;
    if (type === 'close') this.onclose = null;
    if (type === 'message') this.onmessage = null;
    if (type === 'error') this.onerror = null;
  }
}

// Test data generators
export const generateTestBot = (overrides: Partial<any> = {}) => ({
  id: `bot-${Math.random().toString(36).substr(2, 9)}`,
  name: 'Test Bot',
  description: 'A test bot for testing',
  isActive: true,
  settings: {
    primaryColor: '#4f46e5',
    secondaryColor: '#f3f4f6',
    greetingMessage: 'Hello! How can I help you?',
  },
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
});

export const generateTestDocument = (overrides: Partial<any> = {}) => ({
  id: `doc-${Math.random().toString(36).substr(2, 9)}`,
  title: 'Test Document',
  description: 'A test document for testing',
  processingStatus: 'completed',
  uploadedAt: new Date().toISOString(),
  fileSize: 1024,
  fileType: 'application/pdf',
  ...overrides,
});

export const generateTestMessage = (overrides: Partial<any> = {}) => ({
  id: `msg-${Math.random().toString(36).substr(2, 9)}`,
  content: 'Test message content',
  timestamp: new Date().toISOString(),
  isBot: false,
  ...overrides,
});

// Re-export testing library utilities
export * from '@testing-library/react';
export { userEvent } from '@testing-library/user-event';

/**
 * Supabase Database Types
 *
 * Generated types for Supabase database schema.
 * Run `npm run types:supabase` to regenerate these types.
 */

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string | null
          company_name: string | null
          role: 'admin' | 'client' | 'viewer'
          avatar_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          full_name?: string | null
          company_name?: string | null
          role?: 'admin' | 'client' | 'viewer'
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          company_name?: string | null
          role?: 'admin' | 'client' | 'viewer'
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      bots: {
        Row: {
          id: string
          user_id: string
          name: string
          type: 'faq' | 'lead_generation'
          status: 'active' | 'inactive'
          config: <PERSON><PERSON>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          type: 'faq' | 'lead_generation'
          status?: 'active' | 'inactive'
          config?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          type?: 'faq' | 'lead_generation'
          status?: 'active' | 'inactive'
          config?: Json
          created_at?: string
          updated_at?: string
        }
      }
      conversations: {
        Row: {
          id: string
          bot_id: string
          user_id: string | null
          status: 'active' | 'ended'
          metadata: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          bot_id: string
          user_id?: string | null
          status?: 'active' | 'ended'
          metadata?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          bot_id?: string
          user_id?: string | null
          status?: 'active' | 'ended'
          metadata?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      messages: {
        Row: {
          id: string
          conversation_id: string
          content: string
          role: 'user' | 'assistant'
          metadata: Json | null
          created_at: string
        }
        Insert: {
          id?: string
          conversation_id: string
          content: string
          role: 'user' | 'assistant'
          metadata?: Json | null
          created_at?: string
        }
        Update: {
          id?: string
          conversation_id?: string
          content?: string
          role?: 'user' | 'assistant'
          metadata?: Json | null
          created_at?: string
        }
      }
      knowledge_documents: {
        Row: {
          id: string
          bot_id: string
          title: string
          content: string
          file_url: string | null
          file_type: string | null
          status: 'processing' | 'ready' | 'error'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          bot_id: string
          title: string
          content: string
          file_url?: string | null
          file_type?: string | null
          status?: 'processing' | 'ready' | 'error'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          bot_id?: string
          title?: string
          content?: string
          file_url?: string | null
          file_type?: string | null
          status?: 'processing' | 'ready' | 'error'
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      user_role: 'admin' | 'client' | 'viewer'
      bot_type: 'faq' | 'lead_generation'
      bot_status: 'active' | 'inactive'
      conversation_status: 'active' | 'ended'
      message_role: 'user' | 'assistant'
      document_status: 'processing' | 'ready' | 'error'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
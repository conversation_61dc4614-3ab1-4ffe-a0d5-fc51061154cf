/**
 * Standalone Widget Component
 * 
 * React component for the embeddable chat widget that runs independently
 * on external websites with full isolation and security.
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { StandaloneWidgetProps, WidgetMessage } from './types';
import { sanitizeMessage, validateMessage, rateLimiter } from './utils/security';
import { apiClient } from '../services/api';
import { wsClient } from '../services/websocket';

/**
 * Standalone Widget Component
 */
export const StandaloneWidget: React.FC<StandaloneWidgetProps> = ({
  config,
  isOpen,
  sessionId,
  onToggle,
  onMessage,
  onError,
}) => {
  const [messages, setMessages] = useState<WidgetMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  // Initialize widget
  useEffect(() => {
    initializeWidget();
    return () => {
      cleanup();
    };
  }, []);

  // Handle WebSocket connection
  useEffect(() => {
    if (isOpen && config.botId) {
      connectWebSocket();
    }
    return () => {
      disconnectWebSocket();
    };
  }, [isOpen, config.botId]);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Focus input when widget opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  /**
   * Initialize the widget with greeting message
   */
  const initializeWidget = useCallback(() => {
    if (config.greetingMessage) {
      const greetingMessage: WidgetMessage = {
        id: `greeting_${Date.now()}`,
        content: config.greetingMessage,
        sender: 'bot',
        timestamp: new Date(),
        type: 'text',
      };
      setMessages([greetingMessage]);
    }
  }, [config.greetingMessage]);

  /**
   * Connect to WebSocket for real-time communication
   */
  const connectWebSocket = useCallback(async () => {
    try {
      // Update API client configuration
      apiClient.updateConfig({
        baseURL: config.apiEndpoint || 'https://api.webton-chatbots.com/v1',
      });

      // Connect WebSocket
      await wsClient.connect();
      wsClient.joinConversation(sessionId, config.botId);
      setIsConnected(true);

      // Set up WebSocket event listeners
      wsClient.on('message_received', handleBotMessage);
      wsClient.on('bot_typing', handleBotTyping);
      wsClient.on('error', handleWebSocketError);
      wsClient.on('disconnected', handleWebSocketDisconnect);

    } catch (error) {
      console.error('[WebtonChatbot] Failed to connect WebSocket:', error);
      setIsConnected(false);
      onError('Failed to connect to chat service');
    }
  }, [config.apiEndpoint, config.botId, sessionId, onError]);

  /**
   * Disconnect WebSocket
   */
  const disconnectWebSocket = useCallback(() => {
    if (isConnected) {
      wsClient.leaveConversation(sessionId);
      wsClient.removeAllListeners();
      setIsConnected(false);
    }
  }, [isConnected, sessionId]);

  /**
   * Handle bot message from WebSocket
   */
  const handleBotMessage = useCallback((data: any) => {
    if (data.session_id === sessionId) {
      const botMessage: WidgetMessage = {
        id: data.message.id || `bot_${Date.now()}`,
        content: data.message.content,
        sender: 'bot',
        timestamp: new Date(data.message.timestamp || Date.now()),
        type: data.message.type || 'text',
        metadata: data.message.metadata,
      };
      
      setMessages(prev => [...prev, botMessage]);
      setIsTyping(false);
      onMessage(botMessage.content, 'bot');
    }
  }, [sessionId, onMessage]);

  /**
   * Handle bot typing indicator
   */
  const handleBotTyping = useCallback((data: any) => {
    if (data.session_id === sessionId) {
      setIsTyping(true);
      
      // Clear typing after 3 seconds
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      typingTimeoutRef.current = setTimeout(() => {
        setIsTyping(false);
      }, 3000);
    }
  }, [sessionId]);

  /**
   * Handle WebSocket errors
   */
  const handleWebSocketError = useCallback((data: any) => {
    console.error('[WebtonChatbot] WebSocket error:', data.error);
    onError(data.error || 'Connection error');
  }, [onError]);

  /**
   * Handle WebSocket disconnect
   */
  const handleWebSocketDisconnect = useCallback(() => {
    setIsConnected(false);
    setIsTyping(false);
  }, []);

  /**
   * Send a message
   */
  const handleSendMessage = useCallback(async () => {
    const message = inputValue.trim();
    
    if (!message || !config.botId) {
      return;
    }

    // Validate message
    if (!validateMessage(message)) {
      onError('Invalid message content');
      return;
    }

    // Check rate limiting
    if (!rateLimiter.isAllowed(sessionId)) {
      onError('Too many messages. Please wait a moment.');
      return;
    }

    // Sanitize message
    const sanitizedMessage = sanitizeMessage(message);
    
    // Create user message
    const userMessage: WidgetMessage = {
      id: `user_${Date.now()}`,
      content: sanitizedMessage,
      sender: 'user',
      timestamp: new Date(),
      type: 'text',
    };

    // Add to messages
    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);
    onMessage(sanitizedMessage, 'user');

    try {
      if (isConnected) {
        // Send via WebSocket for real-time response
        wsClient.sendMessage(sanitizedMessage, sessionId);
      } else {
        // Fallback to HTTP API
        const response = await apiClient.sendMessage(config.botId, {
          message: sanitizedMessage,
          session_id: sessionId,
        });

        const botMessage: WidgetMessage = {
          id: response.message_id || `bot_${Date.now()}`,
          content: response.response,
          sender: 'bot',
          timestamp: new Date(),
          type: 'text',
          metadata: {
            response_time: response.response_time,
            confidence_score: response.confidence_score,
            sources: response.sources,
          },
        };

        setMessages(prev => [...prev, botMessage]);
        onMessage(botMessage.content, 'bot');
      }
    } catch (error) {
      console.error('[WebtonChatbot] Failed to send message:', error);
      onError('Failed to send message. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [inputValue, config.botId, sessionId, isConnected, onMessage, onError]);

  /**
   * Handle key press in input
   */
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  }, [handleSendMessage]);

  /**
   * Scroll to bottom of messages
   */
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  /**
   * Cleanup function
   */
  const cleanup = useCallback(() => {
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    disconnectWebSocket();
  }, [disconnectWebSocket]);

  // Widget styles
  const widgetStyles: React.CSSProperties = {
    fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    fontSize: '14px',
    lineHeight: '1.5',
    color: '#333333',
    backgroundColor: '#ffffff',
    border: '1px solid #e5e7eb',
    borderRadius: '12px',
    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
    overflow: 'hidden',
    width: '350px',
    height: '500px',
    display: 'flex',
    flexDirection: 'column',
    position: 'relative',
  };

  const headerStyles: React.CSSProperties = {
    backgroundColor: config.primaryColor || '#4f46e5',
    color: '#ffffff',
    padding: '16px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
  };

  const messagesStyles: React.CSSProperties = {
    flex: 1,
    padding: '16px',
    overflowY: 'auto',
    display: 'flex',
    flexDirection: 'column',
    gap: '12px',
  };

  const inputAreaStyles: React.CSSProperties = {
    padding: '16px',
    borderTop: '1px solid #e5e7eb',
    display: 'flex',
    gap: '8px',
    alignItems: 'center',
  };

  const inputStyles: React.CSSProperties = {
    flex: 1,
    padding: '8px 12px',
    border: '1px solid #d1d5db',
    borderRadius: '6px',
    fontSize: '14px',
    outline: 'none',
    resize: 'none',
  };

  const buttonStyles: React.CSSProperties = {
    backgroundColor: config.primaryColor || '#4f46e5',
    color: '#ffffff',
    border: 'none',
    borderRadius: '6px',
    padding: '8px 12px',
    fontSize: '14px',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: '40px',
    height: '36px',
  };

  if (!isOpen) {
    // Chat bubble when closed
    const bubbleStyles: React.CSSProperties = {
      width: '60px',
      height: '60px',
      backgroundColor: config.primaryColor || '#4f46e5',
      borderRadius: '50%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      cursor: 'pointer',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      color: '#ffffff',
      fontSize: '24px',
      transition: 'transform 0.2s ease',
    };

    return (
      <div
        style={bubbleStyles}
        onClick={onToggle}
        onMouseEnter={(e) => {
          e.currentTarget.style.transform = 'scale(1.05)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform = 'scale(1)';
        }}
        role="button"
        aria-label="Open chat"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            onToggle();
          }
        }}
      >
        💬
      </div>
    );
  }

  return (
    <div style={widgetStyles} role="dialog" aria-label="Chat widget">
      {/* Header */}
      <div style={headerStyles}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          {config.logo && (
            <img
              src={config.logo}
              alt="Bot avatar"
              style={{ width: '32px', height: '32px', borderRadius: '50%' }}
            />
          )}
          <div>
            <div style={{ fontWeight: '600', fontSize: '16px' }}>
              {config.botName || 'AI Assistant'}
            </div>
            <div style={{ fontSize: '12px', opacity: 0.8 }}>
              {isConnected ? 'Online' : 'Connecting...'}
            </div>
          </div>
        </div>
        <button
          onClick={onToggle}
          style={{
            background: 'none',
            border: 'none',
            color: '#ffffff',
            fontSize: '20px',
            cursor: 'pointer',
            padding: '4px',
            borderRadius: '4px',
          }}
          aria-label="Close chat"
        >
          ×
        </button>
      </div>

      {/* Messages */}
      <div style={messagesStyles}>
        {messages.map((message) => (
          <div
            key={message.id}
            style={{
              display: 'flex',
              justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start',
            }}
          >
            <div
              style={{
                maxWidth: '80%',
                padding: '8px 12px',
                borderRadius: '12px',
                backgroundColor: message.sender === 'user' 
                  ? config.primaryColor || '#4f46e5'
                  : '#f3f4f6',
                color: message.sender === 'user' ? '#ffffff' : '#374151',
                fontSize: '14px',
                lineHeight: '1.4',
              }}
            >
              {message.content}
              <div
                style={{
                  fontSize: '11px',
                  opacity: 0.7,
                  marginTop: '4px',
                }}
              >
                {message.timestamp.toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </div>
            </div>
          </div>
        ))}
        
        {isTyping && (
          <div style={{ display: 'flex', justifyContent: 'flex-start' }}>
            <div
              style={{
                padding: '8px 12px',
                borderRadius: '12px',
                backgroundColor: '#f3f4f6',
                color: '#6b7280',
                fontSize: '14px',
              }}
            >
              Typing...
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div style={inputAreaStyles}>
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Type your message..."
          style={inputStyles}
          disabled={isLoading}
          maxLength={config.maxMessageLength || 1000}
        />
        <button
          onClick={handleSendMessage}
          disabled={!inputValue.trim() || isLoading}
          style={{
            ...buttonStyles,
            opacity: !inputValue.trim() || isLoading ? 0.5 : 1,
            cursor: !inputValue.trim() || isLoading ? 'not-allowed' : 'pointer',
          }}
          aria-label="Send message"
        >
          {isLoading ? '...' : '→'}
        </button>
      </div>
    </div>
  );
};

/**
 * Playwright Global Teardown
 * 
 * Runs after all tests to clean up the testing environment.
 */

import { FullConfig } from '@playwright/test';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global test teardown...');

  try {
    // Clean up test data, close connections, etc.
    // This is where you could clean up test users, reset databases, etc.
    
    console.log('✅ Global teardown completed');
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw here as it might mask test failures
  }
}

export default globalTeardown;

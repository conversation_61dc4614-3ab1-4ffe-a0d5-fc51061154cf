/**
 * Knowledge Base Manager Component
 * 
 * Main component for managing knowledge base documents including upload,
 * listing, preview, editing, and deletion functionality.
 */

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Upload,
  FileText,
  AlertCircle,
  CheckCircle,
  Plus,
  RefreshCw,
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { FileUpload, type FileUploadData } from './FileUpload';
import { DocumentList } from './DocumentList';
import { DocumentPreview } from './DocumentPreview';
import { apiClient } from '@/services/api';
import type { Database } from '@/types/supabase';

type KnowledgeDocument = Database['public']['Tables']['knowledge_documents']['Row'];

interface KnowledgeBaseManagerProps {
  botId: string;
  className?: string;
}

export const KnowledgeBaseManager: React.FC<KnowledgeBaseManagerProps> = ({
  botId,
  className,
}) => {
  const { user, canManageBots } = useAuth();
  const [documents, setDocuments] = useState<KnowledgeDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [showUpload, setShowUpload] = useState(false);
  const [previewDocument, setPreviewDocument] = useState<KnowledgeDocument | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Load documents on component mount
  useEffect(() => {
    loadDocuments();
  }, [botId]);

  const loadDocuments = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // For now, we'll use mock data since the backend isn't implemented yet
      // In a real implementation, this would call: apiClient.getDocuments(botId)
      
      // Mock data for demonstration
      const mockDocuments: KnowledgeDocument[] = [
        {
          id: '1',
          bot_id: botId,
          title: 'Product Manual',
          description: 'Complete product documentation and user guide',
          content: 'This is the product manual content...\n\nSection 1: Getting Started\nWelcome to our product...',
          file_path: '/uploads/product-manual.pdf',
          file_url: null,
          file_type: 'application/pdf',
          file_size: 2048576,
          processing_status: 'ready',
          embedding_status: 'ready',
          created_at: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
          updated_at: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        },
        {
          id: '2',
          bot_id: botId,
          title: 'FAQ Document',
          description: 'Frequently asked questions and answers',
          content: 'Q: How do I get started?\nA: Follow these steps...\n\nQ: What are the requirements?\nA: You need...',
          file_path: '/uploads/faq.txt',
          file_url: null,
          file_type: 'text/plain',
          file_size: 15360,
          processing_status: 'ready',
          embedding_status: 'ready',
          created_at: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
          updated_at: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
        },
        {
          id: '3',
          bot_id: botId,
          title: 'Processing Document',
          description: 'A document currently being processed',
          content: null,
          file_path: '/uploads/processing-doc.docx',
          file_url: null,
          file_type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          file_size: 1024000,
          processing_status: 'processing',
          embedding_status: 'pending',
          created_at: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
          updated_at: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
        },
      ];

      setDocuments(mockDocuments);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load documents');
    } finally {
      setLoading(false);
    }
  };

  const handleUpload = async (uploadData: FileUploadData) => {
    try {
      setUploading(true);
      setError(null);

      // For now, we'll simulate the upload process
      // In a real implementation, this would call: apiClient.uploadDocument(botId, uploadData)
      
      // Simulate upload delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Create mock uploaded document
      const newDocument: KnowledgeDocument = {
        id: Math.random().toString(36).substr(2, 9),
        bot_id: botId,
        title: uploadData.title,
        description: uploadData.description || null,
        content: null, // Will be processed
        file_path: `/uploads/${uploadData.file.name}`,
        file_url: null,
        file_type: uploadData.file.type,
        file_size: uploadData.file.size,
        processing_status: 'processing',
        embedding_status: 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      setDocuments(prev => [newDocument, ...prev]);
      setSuccess(`Successfully uploaded "${uploadData.title}"`);
      setShowUpload(false);

      // Simulate processing completion after 5 seconds
      setTimeout(() => {
        setDocuments(prev => prev.map(doc => 
          doc.id === newDocument.id 
            ? { 
                ...doc, 
                processing_status: 'ready',
                embedding_status: 'ready',
                content: `Content of ${uploadData.title}...\n\nThis is the extracted content from the uploaded file.`
              }
            : doc
        ));
      }, 5000);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to upload document');
    } finally {
      setUploading(false);
    }
  };

  const handleEdit = async (document: KnowledgeDocument, content?: string) => {
    try {
      setError(null);

      // For now, we'll simulate the edit process
      // In a real implementation, this would call: apiClient.updateDocument(document.id, updates)

      const updatedDocument = {
        ...document,
        content: content || document.content,
        updated_at: new Date().toISOString(),
      };

      setDocuments(prev => prev.map(doc => 
        doc.id === document.id ? updatedDocument : doc
      ));

      setSuccess(`Successfully updated "${document.title}"`);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update document');
    }
  };

  const handleDelete = async (documentId: string) => {
    try {
      setError(null);

      // For now, we'll simulate the delete process
      // In a real implementation, this would call: apiClient.deleteKnowledgeDocument(documentId)

      const document = documents.find(d => d.id === documentId);
      setDocuments(prev => prev.filter(doc => doc.id !== documentId));
      setSuccess(`Successfully deleted "${document?.title}"`);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete document');
    }
  };

  const handlePreview = (document: KnowledgeDocument) => {
    setPreviewDocument(document);
  };

  const handleDownload = (doc: KnowledgeDocument) => {
    // For now, we'll simulate the download
    // In a real implementation, this would download the actual file
    const blob = new Blob([doc.content || 'No content available'], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = window.document.createElement('a');
    a.href = url;
    a.download = `${doc.title}.txt`;
    window.document.body.appendChild(a);
    a.click();
    window.document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const clearMessages = () => {
    setError(null);
    setSuccess(null);
  };

  if (!canManageBots) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              You don't have permission to manage knowledge base documents.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className}>
      {/* Messages */}
      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
          <Button
            variant="ghost"
            size="sm"
            className="ml-auto"
            onClick={clearMessages}
          >
            ×
          </Button>
        </Alert>
      )}

      {success && (
        <Alert className="mb-4 border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">{success}</AlertDescription>
          <Button
            variant="ghost"
            size="sm"
            className="ml-auto"
            onClick={clearMessages}
          >
            ×
          </Button>
        </Alert>
      )}

      {/* Header */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Knowledge Base Management
              </CardTitle>
              <CardDescription>
                Upload and manage documents that your chatbot can use to answer questions.
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={loadDocuments}
                disabled={loading}
              >
                <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button
                onClick={() => setShowUpload(true)}
                disabled={uploading}
              >
                <Plus className="mr-2 h-4 w-4" />
                Upload Documents
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Document List */}
      <DocumentList
        documents={documents}
        loading={loading}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onPreview={handlePreview}
        onDownload={handleDownload}
      />

      {/* Upload Dialog */}
      <Dialog open={showUpload} onOpenChange={setShowUpload}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Upload Documents
            </DialogTitle>
            <DialogDescription>
              Upload documents to expand your chatbot's knowledge base. Supported formats include PDF, DOC, TXT, and more.
            </DialogDescription>
          </DialogHeader>
          <FileUpload
            onUpload={handleUpload}
            onCancel={() => setShowUpload(false)}
            maxFileSize={10}
          />
        </DialogContent>
      </Dialog>

      {/* Preview Dialog */}
      <DocumentPreview
        document={previewDocument}
        open={!!previewDocument}
        onClose={() => setPreviewDocument(null)}
        onEdit={handleEdit}
        onDownload={handleDownload}
      />
    </div>
  );
};

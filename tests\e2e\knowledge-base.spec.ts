/**
 * Knowledge Base End-to-End Tests
 * 
 * Tests for complete knowledge base workflows including file upload,
 * document management, search, and preview functionality.
 */

import { test, expect } from '@playwright/test';
import path from 'path';

test.describe('Knowledge Base Management', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.route('**/auth/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            user: { id: '123', email: '<EMAIL>' },
            token: 'mock-jwt-token',
          },
        }),
      });
    });

    // Login and navigate to knowledge base
    await page.goto('/');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // Navigate to knowledge base
    await page.click('text=Knowledge Base');
    await expect(page).toHaveURL(/knowledge/);
  });

  test('should display knowledge base interface', async ({ page }) => {
    // Should show main knowledge base components
    await expect(page.locator('h1')).toContainText(/knowledge base/i);
    await expect(page.locator('[data-testid="file-upload-area"]')).toBeVisible();
    await expect(page.locator('[data-testid="documents-list"]')).toBeVisible();
    await expect(page.locator('input[placeholder*="Search"]')).toBeVisible();
  });

  test('should handle file upload via drag and drop', async ({ page }) => {
    // Mock file upload response
    await page.route('**/knowledge/documents', async route => {
      await route.fulfill({
        status: 201,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            document: {
              id: 'doc-123',
              title: 'test-document.pdf',
              processingStatus: 'processing',
              uploadedAt: new Date().toISOString(),
            },
          },
        }),
      });
    });

    // Create a test file
    const fileContent = 'This is a test PDF content';
    const fileName = 'test-document.pdf';

    // Simulate file drop
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles({
      name: fileName,
      mimeType: 'application/pdf',
      buffer: Buffer.from(fileContent),
    });

    // Fill in metadata
    await page.fill('input[placeholder="Document title"]', 'Test Document');
    await page.fill('textarea[placeholder*="description"]', 'This is a test document for upload');

    // Click upload button
    await page.click('button:has-text("Upload Files")');

    // Should show upload progress and success
    await expect(page.locator('text=Uploading')).toBeVisible();
    await expect(page.locator('text=Upload successful')).toBeVisible();
  });

  test('should validate file types and sizes', async ({ page }) => {
    // Try to upload an invalid file type
    const invalidFile = {
      name: 'test.exe',
      mimeType: 'application/x-executable',
      buffer: Buffer.from('invalid content'),
    };

    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(invalidFile);

    // Should show error message
    await expect(page.locator('text=Invalid file type')).toBeVisible();
    await expect(page.locator('text=Supported formats')).toBeVisible();
  });

  test('should display and manage documents list', async ({ page }) => {
    // Mock documents list response
    await page.route('**/knowledge/documents**', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: [
            {
              id: 'doc-1',
              title: 'Document 1',
              description: 'First test document',
              processingStatus: 'completed',
              uploadedAt: '2023-01-01T00:00:00Z',
              fileSize: 1024,
              fileType: 'application/pdf',
            },
            {
              id: 'doc-2',
              title: 'Document 2',
              description: 'Second test document',
              processingStatus: 'processing',
              uploadedAt: '2023-01-02T00:00:00Z',
              fileSize: 2048,
              fileType: 'text/plain',
            },
          ],
        }),
      });
    });

    // Reload to fetch documents
    await page.reload();

    // Should display documents
    await expect(page.locator('text=Document 1')).toBeVisible();
    await expect(page.locator('text=Document 2')).toBeVisible();
    await expect(page.locator('text=First test document')).toBeVisible();

    // Should show processing status
    await expect(page.locator('[data-testid="status-completed"]')).toBeVisible();
    await expect(page.locator('[data-testid="status-processing"]')).toBeVisible();
  });

  test('should search and filter documents', async ({ page }) => {
    // Mock search response
    await page.route('**/knowledge/documents**', async route => {
      const url = new URL(route.request().url());
      const search = url.searchParams.get('search');
      
      const allDocs = [
        { id: 'doc-1', title: 'User Manual', description: 'Product user manual' },
        { id: 'doc-2', title: 'API Documentation', description: 'Technical API docs' },
        { id: 'doc-3', title: 'FAQ Document', description: 'Frequently asked questions' },
      ];

      const filteredDocs = search 
        ? allDocs.filter(doc => 
            doc.title.toLowerCase().includes(search.toLowerCase()) ||
            doc.description.toLowerCase().includes(search.toLowerCase())
          )
        : allDocs;

      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: filteredDocs,
        }),
      });
    });

    // Initial load should show all documents
    await page.reload();
    await expect(page.locator('text=User Manual')).toBeVisible();
    await expect(page.locator('text=API Documentation')).toBeVisible();
    await expect(page.locator('text=FAQ Document')).toBeVisible();

    // Search for specific document
    await page.fill('input[placeholder*="Search"]', 'API');
    await page.press('input[placeholder*="Search"]', 'Enter');

    // Should show only matching documents
    await expect(page.locator('text=API Documentation')).toBeVisible();
    await expect(page.locator('text=User Manual')).not.toBeVisible();
    await expect(page.locator('text=FAQ Document')).not.toBeVisible();
  });

  test('should preview documents', async ({ page }) => {
    // Mock document content response
    await page.route('**/knowledge/documents/doc-1/content', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            content: 'This is the document content for preview...',
            metadata: {
              pages: 5,
              wordCount: 1250,
              lastModified: '2023-01-01T00:00:00Z',
            },
          },
        }),
      });
    });

    // Mock documents list
    await page.route('**/knowledge/documents**', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: [
            {
              id: 'doc-1',
              title: 'Test Document',
              description: 'A test document',
              processingStatus: 'completed',
            },
          ],
        }),
      });
    });

    await page.reload();

    // Click preview button
    await page.click('[data-testid="preview-doc-1"]');

    // Should open preview modal/panel
    await expect(page.locator('[data-testid="document-preview"]')).toBeVisible();
    await expect(page.locator('text=This is the document content')).toBeVisible();
    await expect(page.locator('text=5 pages')).toBeVisible();
    await expect(page.locator('text=1250 words')).toBeVisible();
  });

  test('should delete documents', async ({ page }) => {
    // Mock delete response
    await page.route('**/knowledge/documents/doc-1', async route => {
      if (route.request().method() === 'DELETE') {
        await route.fulfill({
          status: 204,
          body: '',
        });
      }
    });

    // Mock documents list
    await page.route('**/knowledge/documents**', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: [
            {
              id: 'doc-1',
              title: 'Document to Delete',
              description: 'This will be deleted',
              processingStatus: 'completed',
            },
          ],
        }),
      });
    });

    await page.reload();

    // Click delete button
    await page.click('[data-testid="delete-doc-1"]');

    // Should show confirmation dialog
    await expect(page.locator('text=Are you sure')).toBeVisible();
    await expect(page.locator('text=This action cannot be undone')).toBeVisible();

    // Confirm deletion
    await page.click('button:has-text("Delete")');

    // Should show success message
    await expect(page.locator('text=Document deleted successfully')).toBeVisible();
  });

  test('should handle upload errors gracefully', async ({ page }) => {
    // Mock upload error response
    await page.route('**/knowledge/documents', async route => {
      await route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: {
            code: 'FILE_TOO_LARGE',
            message: 'File size exceeds maximum limit',
          },
        }),
      });
    });

    // Try to upload a file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles({
      name: 'large-file.pdf',
      mimeType: 'application/pdf',
      buffer: Buffer.from('large file content'),
    });

    await page.fill('input[placeholder="Document title"]', 'Large File');
    await page.click('button:has-text("Upload Files")');

    // Should show error message
    await expect(page.locator('text=File size exceeds maximum limit')).toBeVisible();
    await expect(page.locator('text=Upload failed')).toBeVisible();
  });

  test('should show processing status updates', async ({ page }) => {
    // Mock documents with different processing statuses
    await page.route('**/knowledge/documents**', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: [
            {
              id: 'doc-1',
              title: 'Processing Document',
              processingStatus: 'processing',
              processingProgress: 45,
            },
            {
              id: 'doc-2',
              title: 'Failed Document',
              processingStatus: 'failed',
              processingError: 'Unable to extract text from PDF',
            },
            {
              id: 'doc-3',
              title: 'Completed Document',
              processingStatus: 'completed',
            },
          ],
        }),
      });
    });

    await page.reload();

    // Should show different status indicators
    await expect(page.locator('[data-testid="status-processing"]')).toBeVisible();
    await expect(page.locator('[data-testid="status-failed"]')).toBeVisible();
    await expect(page.locator('[data-testid="status-completed"]')).toBeVisible();

    // Should show progress for processing documents
    await expect(page.locator('text=45%')).toBeVisible();

    // Should show error message for failed documents
    await expect(page.locator('text=Unable to extract text')).toBeVisible();
  });
});

/**
 * Mock Analytics Data Service
 * 
 * Provides mock data for analytics dashboard development.
 * This will be replaced with real API calls once backend is implemented.
 */

import { 
  AnalyticsMetrics, 
  TimeSeriesData, 
  GetAnalyticsResponse,
  AnalyticsEvent 
} from '../types/api';

/**
 * Generate mock time series data for charts
 */
export function generateMockTimeSeriesData(
  days: number = 30,
  granularity: 'hour' | 'day' | 'week' | 'month' = 'day'
): TimeSeriesData[] {
  const data: TimeSeriesData[] = [];
  const now = new Date();
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);
    
    // Generate realistic mock data with some variance
    const baseConversations = 50 + Math.floor(Math.random() * 100);
    const baseMessages = baseConversations * (2 + Math.random() * 4);
    const baseResponseTime = 800 + Math.random() * 400;
    const baseSatisfaction = 3.5 + Math.random() * 1.5;
    
    data.push({
      timestamp: date.toISOString(),
      conversations: Math.floor(baseConversations),
      messages: Math.floor(baseMessages),
      response_time: Math.floor(baseResponseTime),
      satisfaction_score: Math.round(baseSatisfaction * 10) / 10,
    });
  }
  
  return data;
}

/**
 * Generate mock analytics metrics
 */
export function generateMockAnalyticsMetrics(): AnalyticsMetrics {
  return {
    total_conversations: 1245 + Math.floor(Math.random() * 500),
    total_messages: 8934 + Math.floor(Math.random() * 2000),
    unique_users: 892 + Math.floor(Math.random() * 300),
    average_response_time: 850 + Math.floor(Math.random() * 300),
    satisfaction_score: 4.2 + Math.random() * 0.6,
    resolution_rate: 0.85 + Math.random() * 0.1,
  };
}

/**
 * Generate mock analytics events
 */
export function generateMockAnalyticsEvents(count: number = 100): AnalyticsEvent[] {
  const events: AnalyticsEvent[] = [];
  const eventTypes: AnalyticsEvent['event_type'][] = [
    'conversation_started',
    'conversation_ended',
    'message_sent',
    'message_received',
    'lead_captured',
    'feedback_given'
  ];
  
  for (let i = 0; i < count; i++) {
    const date = new Date();
    date.setHours(date.getHours() - Math.floor(Math.random() * 24 * 7)); // Last 7 days
    
    events.push({
      id: `event_${i + 1}`,
      bot_id: 'demo-bot-123',
      event_type: eventTypes[Math.floor(Math.random() * eventTypes.length)],
      event_data: {
        session_id: `session_${Math.floor(Math.random() * 1000)}`,
        user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        page_url: `https://example.com/page${Math.floor(Math.random() * 10)}`,
      },
      user_identifier: Math.random() > 0.5 ? `user_${Math.floor(Math.random() * 500)}` : undefined,
      session_id: `session_${Math.floor(Math.random() * 1000)}`,
      created_at: date.toISOString(),
    });
  }
  
  return events.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
}

/**
 * Mock API service for analytics data
 */
export class MockAnalyticsService {
  /**
   * Get analytics data for a date range
   */
  static async getAnalytics(
    botId: string,
    startDate: string,
    endDate: string,
    granularity: 'hour' | 'day' | 'week' | 'month' = 'day'
  ): Promise<GetAnalyticsResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));
    
    const start = new Date(startDate);
    const end = new Date(endDate);
    const daysDiff = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
    
    return {
      period: {
        start_date: startDate,
        end_date: endDate,
      },
      metrics: generateMockAnalyticsMetrics(),
      time_series: generateMockTimeSeriesData(daysDiff, granularity),
    };
  }
  
  /**
   * Get recent analytics events
   */
  static async getRecentEvents(
    botId: string,
    limit: number = 50
  ): Promise<AnalyticsEvent[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 500));
    
    return generateMockAnalyticsEvents(limit);
  }
  
  /**
   * Get conversation funnel data
   */
  static async getConversationFunnel(
    botId: string,
    startDate: string,
    endDate: string
  ): Promise<{
    stage: string;
    count: number;
    percentage: number;
  }[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 400 + Math.random() * 600));
    
    const totalVisitors = 5000 + Math.floor(Math.random() * 2000);
    
    return [
      {
        stage: 'Page Visitors',
        count: totalVisitors,
        percentage: 100,
      },
      {
        stage: 'Widget Opened',
        count: Math.floor(totalVisitors * 0.25),
        percentage: 25,
      },
      {
        stage: 'Started Conversation',
        count: Math.floor(totalVisitors * 0.18),
        percentage: 18,
      },
      {
        stage: 'Completed Conversation',
        count: Math.floor(totalVisitors * 0.15),
        percentage: 15,
      },
      {
        stage: 'Provided Contact Info',
        count: Math.floor(totalVisitors * 0.08),
        percentage: 8,
      },
    ];
  }
  
  /**
   * Get popular topics/questions
   */
  static async getPopularTopics(
    botId: string,
    startDate: string,
    endDate: string,
    limit: number = 10
  ): Promise<{
    topic: string;
    count: number;
    percentage: number;
  }[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 350 + Math.random() * 450));
    
    const topics = [
      'Pricing and Plans',
      'Product Features',
      'Technical Support',
      'Account Management',
      'Billing Questions',
      'Integration Help',
      'API Documentation',
      'Troubleshooting',
      'Feature Requests',
      'General Information',
    ];
    
    const totalQuestions = 2500 + Math.floor(Math.random() * 1000);
    
    return topics.slice(0, limit).map((topic, index) => {
      const baseCount = Math.floor(totalQuestions * (0.3 - index * 0.03));
      const count = baseCount + Math.floor(Math.random() * 100);
      
      return {
        topic,
        count,
        percentage: Math.round((count / totalQuestions) * 100 * 10) / 10,
      };
    });
  }
  
  /**
   * Get user satisfaction breakdown
   */
  static async getSatisfactionBreakdown(
    botId: string,
    startDate: string,
    endDate: string
  ): Promise<{
    rating: number;
    count: number;
    percentage: number;
  }[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 400));
    
    const totalRatings = 850 + Math.floor(Math.random() * 300);
    
    // Generate realistic satisfaction distribution (skewed towards positive)
    const distribution = [
      { rating: 5, percentage: 45 + Math.random() * 10 },
      { rating: 4, percentage: 25 + Math.random() * 10 },
      { rating: 3, percentage: 15 + Math.random() * 5 },
      { rating: 2, percentage: 8 + Math.random() * 5 },
      { rating: 1, percentage: 7 + Math.random() * 3 },
    ];
    
    return distribution.map(({ rating, percentage }) => {
      const count = Math.floor(totalRatings * (percentage / 100));
      return {
        rating,
        count,
        percentage: Math.round(percentage * 10) / 10,
      };
    });
  }
}

/**
 * Export utilities for analytics data
 */
export const AnalyticsExportUtils = {
  /**
   * Convert analytics data to CSV format
   */
  toCSV(data: any[], filename: string = 'analytics-export'): string {
    if (!data.length) return '';
    
    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header];
          // Escape commas and quotes in CSV
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        }).join(',')
      )
    ].join('\n');
    
    return csvContent;
  },
  
  /**
   * Generate export filename with timestamp
   */
  generateFilename(prefix: string, extension: string = 'csv'): string {
    const now = new Date();
    const timestamp = now.toISOString().split('T')[0]; // YYYY-MM-DD
    return `${prefix}-${timestamp}.${extension}`;
  },
};

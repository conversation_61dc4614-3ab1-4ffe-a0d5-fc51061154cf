import { Suspense } from "react";
import { useRoutes, Routes, Route } from "react-router-dom";
import Home from "./components/home";
import { WidgetConfigProvider } from "./contexts/WidgetConfigContext";
import routes from "tempo-routes";

function App() {
  return (
    <WidgetConfigProvider>
      <Suspense fallback={<p>Loading...</p>}>
        <>
          <Routes>
            <Route path="/" element={<Home />} />
          </Routes>
          {import.meta.env.VITE_TEMPO === "true" && useRoutes(routes)}
        </>
      </Suspense>
    </WidgetConfigProvider>
  );
}

export default App;

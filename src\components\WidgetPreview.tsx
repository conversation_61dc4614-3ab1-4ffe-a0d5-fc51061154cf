import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, Ta<PERSON>Content } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { motion } from "framer-motion";

import ChatWidget from "@/components/ChatWidget/ChatWidget";

interface WidgetPreviewProps {
  widgetConfig?: {
    primaryColor: string;
    secondaryColor: string;
    logo: string;
    greeting: string;
    position: "bottom-right" | "bottom-left";
    botName: string;
  };
}

const WidgetPreview = ({
  widgetConfig = {
    primaryColor: "#4f46e5",
    secondaryColor: "#ffffff",
    logo: "https://api.dicebear.com/7.x/avataaars/svg?seed=chatbot",
    greeting: "Hello! How can I help you today?",
    position: "bottom-right",
    botName: "AI Assistant",
  },
}: WidgetPreviewProps) => {
  const [config, setConfig] = useState(widgetConfig);
  const [activeTab, setActiveTab] = useState("preview");

  const handleConfigChange = (key: string, value: string) => {
    setConfig((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  return (
    <div className="w-full h-full bg-background p-6">
      <h2 className="text-2xl font-bold mb-6">Widget Preview</h2>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="preview">Preview</TabsTrigger>
          <TabsTrigger value="customize">Customize</TabsTrigger>
          <TabsTrigger value="embed">Embed Code</TabsTrigger>
        </TabsList>

        <TabsContent value="preview" className="w-full">
          <div className="relative w-full h-[600px] border rounded-lg overflow-hidden bg-gray-50">
            <div className="absolute top-0 left-0 right-0 h-12 bg-gray-200 flex items-center px-4">
              <div className="flex space-x-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
              </div>
              <div className="flex-1 text-center text-sm text-gray-600">
                example-website.com
              </div>
            </div>

            <div className="pt-12 h-full relative">
              <div className="p-8 h-full overflow-auto">
                <div className="max-w-4xl mx-auto">
                  <h1 className="text-3xl font-bold mb-6">Example Website</h1>
                  <p className="mb-4">
                    This is a preview of how your chatbot widget will appear on
                    your website.
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <Card>
                      <CardContent className="p-6">
                        <h3 className="text-xl font-semibold mb-2">
                          Product Features
                        </h3>
                        <p className="text-gray-600">
                          Lorem ipsum dolor sit amet, consectetur adipiscing
                          elit. Nullam at justo vel nisi fermentum tincidunt.
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-6">
                        <h3 className="text-xl font-semibold mb-2">Services</h3>
                        <p className="text-gray-600">
                          Sed do eiusmod tempor incididunt ut labore et dolore
                          magna aliqua. Ut enim ad minim veniam.
                        </p>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="prose max-w-none">
                    <h2>About Us</h2>
                    <p>
                      Duis aute irure dolor in reprehenderit in voluptate velit
                      esse cillum dolore eu fugiat nulla pariatur. Excepteur
                      sint occaecat cupidatat non proident, sunt in culpa qui
                      officia deserunt mollit anim id est laborum.
                    </p>
                    <p>
                      Ut enim ad minim veniam, quis nostrud exercitation ullamco
                      laboris nisi ut aliquip ex ea commodo consequat. Lorem
                      ipsum dolor sit amet, consectetur adipiscing elit, sed do
                      eiusmod tempor incididunt ut labore et dolore magna
                      aliqua.
                    </p>
                  </div>
                </div>
              </div>

              {/* Position the chat widget based on config */}
              <div
                className={`absolute ${config.position === "bottom-right" ? "right-6" : "left-6"} bottom-6`}
              >
                <ChatWidget
                  companyName="Example Company"
                  logo={config.logo}
                  primaryColor={config.primaryColor}
                  secondaryColor={config.secondaryColor}
                  greetingMessage={config.greeting}
                  position={config.position}
                  isOpen={false}
                />
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="customize" className="w-full">
          <Card>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="botName">Bot Name</Label>
                    <Input
                      id="botName"
                      value={config.botName}
                      onChange={(e) =>
                        handleConfigChange("botName", e.target.value)
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="greeting">Greeting Message</Label>
                    <Input
                      id="greeting"
                      value={config.greeting}
                      onChange={(e) =>
                        handleConfigChange("greeting", e.target.value)
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="logo">Logo URL</Label>
                    <Input
                      id="logo"
                      value={config.logo}
                      onChange={(e) =>
                        handleConfigChange("logo", e.target.value)
                      }
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="primaryColor">Primary Color</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        id="primaryColor"
                        value={config.primaryColor}
                        onChange={(e) =>
                          handleConfigChange("primaryColor", e.target.value)
                        }
                      />
                      <div
                        className="w-10 h-10 rounded border"
                        style={{ backgroundColor: config.primaryColor }}
                      ></div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="secondaryColor">Secondary Color</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        id="secondaryColor"
                        value={config.secondaryColor}
                        onChange={(e) =>
                          handleConfigChange("secondaryColor", e.target.value)
                        }
                      />
                      <div
                        className="w-10 h-10 rounded border"
                        style={{ backgroundColor: config.secondaryColor }}
                      ></div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="position">Widget Position</Label>
                    <Select
                      value={config.position}
                      onValueChange={(value) =>
                        handleConfigChange("position", value)
                      }
                    >
                      <SelectTrigger id="position">
                        <SelectValue placeholder="Select position" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="bottom-right">
                          Bottom Right
                        </SelectItem>
                        <SelectItem value="bottom-left">Bottom Left</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-end">
                <Button onClick={() => setActiveTab("preview")}>
                  Preview Changes
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="embed" className="w-full">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-4">Embed Code</h3>
              <p className="mb-4 text-muted-foreground">
                Copy and paste this code snippet just before the closing{" "}
                <code>&lt;/body&gt;</code> tag on your website.
              </p>

              <div className="bg-muted p-4 rounded-md overflow-x-auto">
                <pre className="text-sm">
                  {`<script src="https://example.com/chatbot-widget.js" 
  data-widget-id="${Math.random().toString(36).substring(2, 10)}"
  data-primary-color="${config.primaryColor}"
  data-secondary-color="${config.secondaryColor}"
  data-position="${config.position}"
  data-bot-name="${config.botName}"
  async>
</script>`}
                </pre>
              </div>

              <div className="mt-6">
                <Button
                  variant="outline"
                  onClick={() => {
                    // Copy to clipboard functionality would go here
                    alert("Code copied to clipboard!");
                  }}
                >
                  Copy to Clipboard
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default WidgetPreview;

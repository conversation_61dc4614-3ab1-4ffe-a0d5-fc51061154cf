/**
 * FileUpload Component Unit Tests
 * 
 * Tests for file upload functionality, validation, and user interactions.
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import FileUpload, { FileUploadData } from '../../../../src/components/KnowledgeBase/FileUpload';

// Mock react-dropzone
vi.mock('react-dropzone', () => ({
  useDropzone: vi.fn(() => ({
    getRootProps: () => ({
      'data-testid': 'dropzone',
    }),
    getInputProps: () => ({
      'data-testid': 'file-input',
    }),
    isDragActive: false,
    isDragAccept: false,
    isDragReject: false,
    acceptedFiles: [],
    rejectedFiles: [],
  })),
}));

describe('FileUpload', () => {
  const mockOnUpload = vi.fn();
  const defaultProps = {
    onUpload: mockOnUpload,
    loading: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render upload area with correct text', () => {
    render(<FileUpload {...defaultProps} />);

    expect(screen.getByText('Drag and drop files here')).toBeInTheDocument();
    expect(screen.getByText('or click to browse')).toBeInTheDocument();
    expect(screen.getByText('Supported formats: PDF, DOC, DOCX, TXT, CSV, MD')).toBeInTheDocument();
    expect(screen.getByText('Maximum file size: 10MB')).toBeInTheDocument();
  });

  it('should show loading state when uploading', () => {
    render(<FileUpload {...defaultProps} loading={true} />);

    expect(screen.getByText('Uploading...')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /upload files/i })).toBeDisabled();
  });

  it('should handle file selection and metadata editing', async () => {
    const user = userEvent.setup();
    
    // Mock useDropzone to simulate file selection
    const mockFile = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    
    vi.mocked(require('react-dropzone').useDropzone).mockReturnValue({
      getRootProps: () => ({ 'data-testid': 'dropzone' }),
      getInputProps: () => ({ 'data-testid': 'file-input' }),
      isDragActive: false,
      isDragAccept: false,
      isDragReject: false,
      acceptedFiles: [mockFile],
      rejectedFiles: [],
    });

    const { rerender } = render(<FileUpload {...defaultProps} />);
    
    // Trigger re-render to simulate file selection
    rerender(<FileUpload {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByDisplayValue('test.pdf')).toBeInTheDocument();
    });

    // Test metadata editing
    const titleInput = screen.getByDisplayValue('test.pdf');
    await user.clear(titleInput);
    await user.type(titleInput, 'Custom Title');

    expect(titleInput).toHaveValue('Custom Title');
  });

  it('should validate file types correctly', async () => {
    const mockInvalidFile = new File(['test'], 'test.exe', { type: 'application/x-executable' });
    
    vi.mocked(require('react-dropzone').useDropzone).mockReturnValue({
      getRootProps: () => ({ 'data-testid': 'dropzone' }),
      getInputProps: () => ({ 'data-testid': 'file-input' }),
      isDragActive: false,
      isDragAccept: false,
      isDragReject: false,
      acceptedFiles: [],
      rejectedFiles: [{ file: mockInvalidFile, errors: [{ code: 'file-invalid-type', message: 'Invalid file type' }] }],
    });

    render(<FileUpload {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText('Invalid file type')).toBeInTheDocument();
    });
  });

  it('should validate file size correctly', async () => {
    const mockLargeFile = new File(['x'.repeat(11 * 1024 * 1024)], 'large.pdf', { 
      type: 'application/pdf' 
    });
    
    vi.mocked(require('react-dropzone').useDropzone).mockReturnValue({
      getRootProps: () => ({ 'data-testid': 'dropzone' }),
      getInputProps: () => ({ 'data-testid': 'file-input' }),
      isDragActive: false,
      isDragAccept: false,
      isDragReject: false,
      acceptedFiles: [],
      rejectedFiles: [{ file: mockLargeFile, errors: [{ code: 'file-too-large', message: 'File too large' }] }],
    });

    render(<FileUpload {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText('File too large')).toBeInTheDocument();
    });
  });

  it('should call onUpload with correct data when uploading', async () => {
    const user = userEvent.setup();
    const mockFile = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    
    vi.mocked(require('react-dropzone').useDropzone).mockReturnValue({
      getRootProps: () => ({ 'data-testid': 'dropzone' }),
      getInputProps: () => ({ 'data-testid': 'file-input' }),
      isDragActive: false,
      isDragAccept: false,
      isDragReject: false,
      acceptedFiles: [mockFile],
      rejectedFiles: [],
    });

    const { rerender } = render(<FileUpload {...defaultProps} />);
    rerender(<FileUpload {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByDisplayValue('test.pdf')).toBeInTheDocument();
    });

    // Add description
    const descriptionInput = screen.getByPlaceholderText('Optional description...');
    await user.type(descriptionInput, 'Test description');

    // Click upload
    const uploadButton = screen.getByRole('button', { name: /upload files/i });
    await user.click(uploadButton);

    expect(mockOnUpload).toHaveBeenCalledWith({
      file: mockFile,
      title: 'test.pdf',
      description: 'Test description',
    });
  });

  it('should remove files when remove button is clicked', async () => {
    const user = userEvent.setup();
    const mockFile = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    
    vi.mocked(require('react-dropzone').useDropzone).mockReturnValue({
      getRootProps: () => ({ 'data-testid': 'dropzone' }),
      getInputProps: () => ({ 'data-testid': 'file-input' }),
      isDragActive: false,
      isDragAccept: false,
      isDragReject: false,
      acceptedFiles: [mockFile],
      rejectedFiles: [],
    });

    const { rerender } = render(<FileUpload {...defaultProps} />);
    rerender(<FileUpload {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByDisplayValue('test.pdf')).toBeInTheDocument();
    });

    // Click remove button
    const removeButton = screen.getByRole('button', { name: /remove/i });
    await user.click(removeButton);

    // File should be removed from the list
    expect(screen.queryByDisplayValue('test.pdf')).not.toBeInTheDocument();
  });

  it('should disable upload button when no files are selected', () => {
    render(<FileUpload {...defaultProps} />);

    const uploadButton = screen.getByRole('button', { name: /upload files/i });
    expect(uploadButton).toBeDisabled();
  });

  it('should show progress indicators during upload', () => {
    const mockFile = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    
    vi.mocked(require('react-dropzone').useDropzone).mockReturnValue({
      getRootProps: () => ({ 'data-testid': 'dropzone' }),
      getInputProps: () => ({ 'data-testid': 'file-input' }),
      isDragActive: false,
      isDragAccept: false,
      isDragReject: false,
      acceptedFiles: [mockFile],
      rejectedFiles: [],
    });

    render(<FileUpload {...defaultProps} loading={true} />);

    expect(screen.getByText('Uploading...')).toBeInTheDocument();
  });
});

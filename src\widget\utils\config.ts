/**
 * Widget Configuration Utilities
 * 
 * Utilities for validating and sanitizing widget configuration.
 */

import { WidgetConfig } from '../types';

/**
 * Default widget configuration
 */
export const DEFAULT_CONFIG: Partial<WidgetConfig> = {
  companyName: 'Company',
  botName: 'AI Assistant',
  primaryColor: '#4f46e5',
  secondaryColor: '#ffffff',
  position: 'bottom-right',
  greetingMessage: 'Hello! How can I help you today?',
  theme: 'light',
  autoOpen: false,
  showBranding: true,
  enableFileUpload: false,
  enableFeedback: true,
  maxMessageLength: 1000,
  debug: false,
  apiEndpoint: 'https://api.webton-chatbots.com/v1',
  websocketEndpoint: 'wss://api.webton-chatbots.com/ws',
};

/**
 * Validate widget configuration
 */
export function validateConfig(config: Partial<WidgetConfig>): WidgetConfig | null {
  try {
    // Check required fields
    if (!config.botId) {
      console.error('[WebtonChatbot] Missing required field: botId');
      return null;
    }

    // Validate botId format
    if (typeof config.botId !== 'string' || config.botId.trim().length === 0) {
      console.error('[WebtonChatbot] Invalid botId format');
      return null;
    }

    // Validate position
    if (config.position && !['bottom-right', 'bottom-left'].includes(config.position)) {
      console.warn('[WebtonChatbot] Invalid position, using default');
      config.position = DEFAULT_CONFIG.position;
    }

    // Validate theme
    if (config.theme && !['light', 'dark', 'auto'].includes(config.theme)) {
      console.warn('[WebtonChatbot] Invalid theme, using default');
      config.theme = DEFAULT_CONFIG.theme;
    }

    // Validate colors
    if (config.primaryColor && !isValidColor(config.primaryColor)) {
      console.warn('[WebtonChatbot] Invalid primary color, using default');
      config.primaryColor = DEFAULT_CONFIG.primaryColor;
    }

    if (config.secondaryColor && !isValidColor(config.secondaryColor)) {
      console.warn('[WebtonChatbot] Invalid secondary color, using default');
      config.secondaryColor = DEFAULT_CONFIG.secondaryColor;
    }

    // Validate URLs
    if (config.apiEndpoint && !isValidUrl(config.apiEndpoint)) {
      console.warn('[WebtonChatbot] Invalid API endpoint, using default');
      config.apiEndpoint = DEFAULT_CONFIG.apiEndpoint;
    }

    if (config.websocketEndpoint && !isValidWebSocketUrl(config.websocketEndpoint)) {
      console.warn('[WebtonChatbot] Invalid WebSocket endpoint, using default');
      config.websocketEndpoint = DEFAULT_CONFIG.websocketEndpoint;
    }

    // Validate logo URL
    if (config.logo && !isValidUrl(config.logo)) {
      console.warn('[WebtonChatbot] Invalid logo URL, removing logo');
      config.logo = undefined;
    }

    // Validate message length
    if (config.maxMessageLength && (config.maxMessageLength < 1 || config.maxMessageLength > 10000)) {
      console.warn('[WebtonChatbot] Invalid max message length, using default');
      config.maxMessageLength = DEFAULT_CONFIG.maxMessageLength;
    }

    // Validate allowed origins
    if (config.allowedOrigins && !Array.isArray(config.allowedOrigins)) {
      console.warn('[WebtonChatbot] Invalid allowed origins format, ignoring');
      config.allowedOrigins = undefined;
    }

    // Merge with defaults
    const validatedConfig: WidgetConfig = {
      ...DEFAULT_CONFIG,
      ...config,
      botId: config.botId, // Ensure botId is preserved
    } as WidgetConfig;

    return validatedConfig;
  } catch (error) {
    console.error('[WebtonChatbot] Configuration validation error:', error);
    return null;
  }
}

/**
 * Sanitize configuration for security
 */
export function sanitizeConfig(config: WidgetConfig): WidgetConfig {
  const sanitized = { ...config };

  // Sanitize string fields
  if (sanitized.companyName) {
    sanitized.companyName = sanitizeString(sanitized.companyName);
  }

  if (sanitized.botName) {
    sanitized.botName = sanitizeString(sanitized.botName);
  }

  if (sanitized.greetingMessage) {
    sanitized.greetingMessage = sanitizeString(sanitized.greetingMessage);
  }

  // Sanitize HTML in strings
  if (sanitized.companyName) {
    sanitized.companyName = stripHtml(sanitized.companyName);
  }

  if (sanitized.botName) {
    sanitized.botName = stripHtml(sanitized.botName);
  }

  if (sanitized.greetingMessage) {
    sanitized.greetingMessage = stripHtml(sanitized.greetingMessage);
  }

  // Ensure URLs are safe
  if (sanitized.apiEndpoint) {
    sanitized.apiEndpoint = sanitizeUrl(sanitized.apiEndpoint);
  }

  if (sanitized.websocketEndpoint) {
    sanitized.websocketEndpoint = sanitizeUrl(sanitized.websocketEndpoint);
  }

  if (sanitized.logo) {
    sanitized.logo = sanitizeUrl(sanitized.logo);
  }

  return sanitized;
}

/**
 * Check if a string is a valid color (hex, rgb, rgba, hsl, hsla, or named color)
 */
function isValidColor(color: string): boolean {
  // Hex colors
  if (/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color)) {
    return true;
  }

  // RGB/RGBA colors
  if (/^rgba?\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*(,\s*[\d.]+)?\s*\)$/.test(color)) {
    return true;
  }

  // HSL/HSLA colors
  if (/^hsla?\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*(,\s*[\d.]+)?\s*\)$/.test(color)) {
    return true;
  }

  // Named colors (basic check)
  const namedColors = [
    'black', 'white', 'red', 'green', 'blue', 'yellow', 'orange', 'purple',
    'pink', 'brown', 'gray', 'grey', 'transparent'
  ];
  
  return namedColors.includes(color.toLowerCase());
}

/**
 * Check if a string is a valid URL
 */
function isValidUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    return ['http:', 'https:'].includes(urlObj.protocol);
  } catch {
    return false;
  }
}

/**
 * Check if a string is a valid WebSocket URL
 */
function isValidWebSocketUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    return ['ws:', 'wss:'].includes(urlObj.protocol);
  } catch {
    return false;
  }
}

/**
 * Sanitize a string by trimming and limiting length
 */
function sanitizeString(str: string, maxLength: number = 200): string {
  if (typeof str !== 'string') {
    return '';
  }
  
  return str.trim().substring(0, maxLength);
}

/**
 * Strip HTML tags from a string
 */
function stripHtml(str: string): string {
  if (typeof str !== 'string') {
    return '';
  }
  
  // Remove HTML tags
  return str.replace(/<[^>]*>/g, '');
}

/**
 * Sanitize URL to prevent XSS
 */
function sanitizeUrl(url: string): string {
  if (typeof url !== 'string') {
    return '';
  }

  // Remove javascript: and data: protocols
  if (/^(javascript|data|vbscript):/i.test(url)) {
    return '';
  }

  return url.trim();
}

/**
 * Get configuration from environment variables (for development)
 */
export function getEnvironmentConfig(): Partial<WidgetConfig> {
  const config: Partial<WidgetConfig> = {};

  // Check for environment variables
  if (typeof window !== 'undefined' && (window as any).__WEBTON_CONFIG__) {
    Object.assign(config, (window as any).__WEBTON_CONFIG__);
  }

  return config;
}

/**
 * Merge multiple configuration objects
 */
export function mergeConfigs(...configs: Partial<WidgetConfig>[]): Partial<WidgetConfig> {
  return configs.reduce((merged, config) => {
    return { ...merged, ...config };
  }, {});
}

/**
 * Check if the current origin is allowed
 */
export function isOriginAllowed(allowedOrigins?: string[]): boolean {
  if (!allowedOrigins || allowedOrigins.length === 0) {
    return true; // No restrictions
  }

  const currentOrigin = window.location.origin;
  
  return allowedOrigins.some(origin => {
    // Exact match
    if (origin === currentOrigin) {
      return true;
    }
    
    // Wildcard match
    if (origin.includes('*')) {
      const pattern = origin.replace(/\*/g, '.*');
      const regex = new RegExp(`^${pattern}$`);
      return regex.test(currentOrigin);
    }
    
    return false;
  });
}

/**
 * Validate configuration against security policies
 */
export function validateSecurityPolicy(config: WidgetConfig): boolean {
  // Check origin restrictions
  if (!isOriginAllowed(config.allowedOrigins)) {
    console.error('[WebtonChatbot] Origin not allowed:', window.location.origin);
    return false;
  }

  // Check for required HTTPS in production
  if (config.apiEndpoint?.startsWith('http:') && window.location.protocol === 'https:') {
    console.error('[WebtonChatbot] Cannot use HTTP API endpoint on HTTPS page');
    return false;
  }

  return true;
}

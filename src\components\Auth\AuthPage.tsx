/**
 * Authentication Page Component
 * 
 * Main authentication page with login, signup, and password reset forms.
 */

import React, { useState } from 'react';
import { LoginForm } from './LoginForm';
import { SignupForm } from './SignupForm';
import { ResetPasswordForm } from './ResetPasswordForm';

type AuthMode = 'login' | 'signup' | 'reset';

interface AuthPageProps {
  initialMode?: AuthMode;
  redirectTo?: string;
}

export const AuthPage: React.FC<AuthPageProps> = ({
  initialMode = 'login',
  redirectTo,
}) => {
  const [mode, setMode] = useState<AuthMode>(initialMode);

  const renderForm = () => {
    switch (mode) {
      case 'login':
        return (
          <LoginForm
            onSwitchToSignup={() => setMode('signup')}
            onSwitchToReset={() => setMode('reset')}
            redirectTo={redirectTo}
          />
        );
      case 'signup':
        return (
          <SignupForm
            onSwitchToLogin={() => setMode('login')}
            redirectTo={redirectTo}
          />
        );
      case 'reset':
        return (
          <ResetPasswordForm
            onSwitchToLogin={() => setMode('login')}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Logo/Branding */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">
            Webton
          </h1>
          <p className="mt-2 text-sm text-gray-600">
            AI Chatbots Platform
          </p>
        </div>

        {/* Auth Form */}
        {renderForm()}

        {/* Footer */}
        <div className="text-center text-xs text-gray-500">
          <p>
            By continuing, you agree to our{' '}
            <a href="/terms" className="underline hover:text-gray-700">
              Terms of Service
            </a>{' '}
            and{' '}
            <a href="/privacy" className="underline hover:text-gray-700">
              Privacy Policy
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

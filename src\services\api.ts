/**
 * API Service Layer
 * 
 * Centralized API client for the Webton AI Chatbots Platform.
 * Handles authentication, request/response processing, and error handling.
 */

import {
  APIClientConfig,
  APIResponse,
  RequestConfig,
  RateLimitInfo,
  User,
  Bot,
  Message,
  Conversation,
  KnowledgeDocument,
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  CreateBotRequest,
  UpdateBotRequest,
  SendMessageRequest,
  SendMessageResponse,
  UploadDocumentRequest,
  UploadDocumentResponse,
  GetAnalyticsRequest,
  GetAnalyticsResponse,
  GetBotsRequest,
  GetConversationsRequest,
  GetDocumentsRequest,
} from '../types/api';

/**
 * Main API client class for interacting with the Webton backend.
 */
export class WebtonAPIClient {
  private config: APIClientConfig;
  private rateLimitInfo: RateLimitInfo | null = null;

  constructor(config: APIClientConfig) {
    this.config = {
      timeout: 10000,
      retries: 3,
      ...config,
    };
  }

  /**
   * Update the API client configuration.
   */
  updateConfig(updates: Partial<APIClientConfig>): void {
    this.config = { ...this.config, ...updates };
  }

  /**
   * Set the access token for authenticated requests.
   */
  setAccessToken(token: string): void {
    this.config.accessToken = token;
  }

  /**
   * Set the API key for API key authentication.
   */
  setAPIKey(apiKey: string): void {
    this.config.apiKey = apiKey;
  }

  /**
   * Get current rate limit information.
   */
  getRateLimitInfo(): RateLimitInfo | null {
    return this.rateLimitInfo;
  }

  /**
   * Make an HTTP request with automatic retry and error handling.
   */
  private async makeRequest<T>(config: RequestConfig): Promise<APIResponse<T>> {
    const url = `${this.config.baseURL}${config.url}`;
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...config.headers,
    };

    // Add authentication headers
    if (this.config.accessToken) {
      headers.Authorization = `Bearer ${this.config.accessToken}`;
    } else if (this.config.apiKey) {
      headers.Authorization = `Bearer ${this.config.apiKey}`;
    }

    const requestOptions: RequestInit = {
      method: config.method,
      headers,
      signal: AbortSignal.timeout(this.config.timeout || 10000),
    };

    // Add body for non-GET requests
    if (config.data && config.method !== 'GET') {
      if (config.data instanceof FormData) {
        delete headers['Content-Type']; // Let browser set multipart boundary
        requestOptions.body = config.data;
      } else {
        requestOptions.body = JSON.stringify(config.data);
      }
    }

    // Add query parameters for GET requests
    if (config.params && config.method === 'GET') {
      const searchParams = new URLSearchParams();
      Object.entries(config.params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
      const queryString = searchParams.toString();
      if (queryString) {
        config.url += `?${queryString}`;
      }
    }

    let lastError: Error;
    const maxRetries = this.config.retries || 3;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const response = await fetch(url, requestOptions);

        // Update rate limit information
        this.updateRateLimitInfo(response.headers);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new APIError(
            errorData.error?.code || 'HTTP_ERROR',
            errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`,
            errorData.error?.details
          );
        }

        const data = await response.json();
        return data;
      } catch (error) {
        lastError = error as Error;

        // Don't retry on authentication errors or client errors
        if (error instanceof APIError) {
          const isRetryable = !['UNAUTHORIZED', 'FORBIDDEN', 'INVALID_REQUEST'].includes(error.code);
          if (!isRetryable || attempt === maxRetries) {
            throw error;
          }
        }

        // Wait before retrying (exponential backoff)
        if (attempt < maxRetries) {
          const delay = Math.pow(2, attempt) * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError!;
  }

  /**
   * Update rate limit information from response headers.
   */
  private updateRateLimitInfo(headers: Headers): void {
    const limit = headers.get('X-RateLimit-Limit');
    const remaining = headers.get('X-RateLimit-Remaining');
    const reset = headers.get('X-RateLimit-Reset');
    const window = headers.get('X-RateLimit-Window');

    if (limit && remaining && reset && window) {
      this.rateLimitInfo = {
        limit: parseInt(limit, 10),
        remaining: parseInt(remaining, 10),
        reset: parseInt(reset, 10),
        window: parseInt(window, 10),
      };
    }
  }

  // ============================================================================
  // Authentication Methods
  // ============================================================================

  /**
   * Login with email and password.
   */
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await this.makeRequest<LoginResponse>({
      method: 'POST',
      url: '/auth/login',
      data: credentials,
    });
    return response.data;
  }

  /**
   * Register a new user account.
   */
  async register(userData: RegisterRequest): Promise<User> {
    const response = await this.makeRequest<User>({
      method: 'POST',
      url: '/auth/register',
      data: userData,
    });
    return response.data;
  }

  /**
   * Refresh access token.
   */
  async refreshToken(refreshToken: string): Promise<LoginResponse> {
    const response = await this.makeRequest<LoginResponse>({
      method: 'POST',
      url: '/auth/refresh',
      data: { refresh_token: refreshToken },
    });
    return response.data;
  }

  /**
   * Logout and invalidate tokens.
   */
  async logout(): Promise<void> {
    await this.makeRequest<void>({
      method: 'POST',
      url: '/auth/logout',
    });
  }

  // ============================================================================
  // Bot Management Methods
  // ============================================================================

  /**
   * Get all bots for the authenticated user.
   */
  async getBots(params?: GetBotsRequest): Promise<APIResponse<Bot[]>> {
    return this.makeRequest<Bot[]>({
      method: 'GET',
      url: '/bots',
      params,
    });
  }

  /**
   * Get a specific bot by ID.
   */
  async getBot(botId: string): Promise<Bot> {
    const response = await this.makeRequest<Bot>({
      method: 'GET',
      url: `/bots/${botId}`,
    });
    return response.data;
  }

  /**
   * Create a new bot.
   */
  async createBot(botData: CreateBotRequest): Promise<Bot> {
    const response = await this.makeRequest<Bot>({
      method: 'POST',
      url: '/bots',
      data: botData,
    });
    return response.data;
  }

  /**
   * Update an existing bot.
   */
  async updateBot(botId: string, updates: UpdateBotRequest): Promise<Bot> {
    const response = await this.makeRequest<Bot>({
      method: 'PUT',
      url: `/bots/${botId}`,
      data: updates,
    });
    return response.data;
  }

  /**
   * Delete a bot.
   */
  async deleteBot(botId: string): Promise<void> {
    await this.makeRequest<void>({
      method: 'DELETE',
      url: `/bots/${botId}`,
    });
  }

  // ============================================================================
  // Conversation Methods
  // ============================================================================

  /**
   * Send a message to a bot and get a response.
   */
  async sendMessage(botId: string, messageData: SendMessageRequest): Promise<SendMessageResponse> {
    const response = await this.makeRequest<SendMessageResponse>({
      method: 'POST',
      url: `/bots/${botId}/messages`,
      data: messageData,
    });
    return response.data;
  }

  /**
   * Get conversations for a bot.
   */
  async getConversations(botId: string, params?: GetConversationsRequest): Promise<APIResponse<Conversation[]>> {
    return this.makeRequest<Conversation[]>({
      method: 'GET',
      url: `/bots/${botId}/conversations`,
      params,
    });
  }

  /**
   * Get a specific conversation.
   */
  async getConversation(botId: string, conversationId: string): Promise<Conversation> {
    const response = await this.makeRequest<Conversation>({
      method: 'GET',
      url: `/bots/${botId}/conversations/${conversationId}`,
    });
    return response.data;
  }

  /**
   * Get messages for a conversation.
   */
  async getConversationMessages(botId: string, conversationId: string): Promise<APIResponse<Message[]>> {
    return this.makeRequest<Message[]>({
      method: 'GET',
      url: `/bots/${botId}/conversations/${conversationId}/messages`,
    });
  }

  // ============================================================================
  // Knowledge Base Methods
  // ============================================================================

  /**
   * Upload a document to a bot's knowledge base.
   */
  async uploadDocument(botId: string, documentData: UploadDocumentRequest): Promise<UploadDocumentResponse> {
    const formData = new FormData();
    formData.append('file', documentData.file);
    if (documentData.title) formData.append('title', documentData.title);
    if (documentData.description) formData.append('description', documentData.description);

    const response = await this.makeRequest<UploadDocumentResponse>({
      method: 'POST',
      url: `/bots/${botId}/knowledge/documents`,
      data: formData,
    });
    return response.data;
  }

  /**
   * Get documents in a bot's knowledge base.
   */
  async getDocuments(botId: string, params?: GetDocumentsRequest): Promise<APIResponse<KnowledgeDocument[]>> {
    return this.makeRequest<KnowledgeDocument[]>({
      method: 'GET',
      url: `/bots/${botId}/knowledge/documents`,
      params,
    });
  }

  /**
   * Update a knowledge base document.
   */
  async updateDocument(documentId: string, updates: Partial<KnowledgeDocument>): Promise<KnowledgeDocument> {
    const response = await this.makeRequest<KnowledgeDocument>({
      method: 'PUT',
      url: `/knowledge/documents/${documentId}`,
      data: updates,
    });
    return response.data;
  }

  /**
   * Delete a knowledge base document.
   */
  async deleteKnowledgeDocument(documentId: string): Promise<void> {
    await this.makeRequest<void>({
      method: 'DELETE',
      url: `/knowledge/documents/${documentId}`,
    });
  }

  /**
   * Get document content for preview.
   */
  async getDocumentContent(documentId: string): Promise<{ content: string }> {
    const response = await this.makeRequest<{ content: string }>({
      method: 'GET',
      url: `/knowledge/documents/${documentId}/content`,
    });
    return response.data;
  }

  /**
   * Update document content.
   */
  async updateDocumentContent(documentId: string, content: string): Promise<void> {
    await this.makeRequest<void>({
      method: 'PUT',
      url: `/knowledge/documents/${documentId}/content`,
      data: { content },
    });
  }

  /**
   * Delete a document from the knowledge base.
   */
  async deleteDocument(botId: string, documentId: string): Promise<void> {
    await this.makeRequest<void>({
      method: 'DELETE',
      url: `/bots/${botId}/knowledge/documents/${documentId}`,
    });
  }

  // ============================================================================
  // Analytics Methods
  // ============================================================================

  /**
   * Get analytics data for a bot.
   */
  async getAnalytics(botId: string, params: GetAnalyticsRequest): Promise<GetAnalyticsResponse> {
    const response = await this.makeRequest<GetAnalyticsResponse>({
      method: 'GET',
      url: `/bots/${botId}/analytics`,
      params,
    });
    return response.data;
  }
}

/**
 * Custom API Error class.
 */
export class APIError extends Error {
  public code: string;
  public details?: Record<string, any>;

  constructor(code: string, message: string, details?: Record<string, any>) {
    super(message);
    this.name = 'APIError';
    this.code = code;
    this.details = details;
  }
}

/**
 * Default API client instance.
 */
export const apiClient = new WebtonAPIClient({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api/v1',
});

/**
 * Utility function to check if an error is an API error.
 */
export function isAPIError(error: unknown): error is APIError {
  return error instanceof APIError;
}

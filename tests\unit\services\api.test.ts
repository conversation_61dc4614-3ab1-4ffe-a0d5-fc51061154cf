/**
 * API Service Unit Tests
 * 
 * Tests for API client functionality, error handling, and request/response processing.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { WebtonAPIClient, APIError, isAPIError } from '../../../src/services/api';

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('WebtonAPIClient', () => {
  let apiClient: WebtonAPIClient;

  beforeEach(() => {
    apiClient = new WebtonAPIClient({
      baseURL: 'https://api.test.com',
      apiKey: 'test-api-key',
    });
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Authentication Methods', () => {
    it('should login with correct credentials', async () => {
      const mockResponse = {
        success: true,
        data: {
          user: { id: '123', email: '<EMAIL>' },
          token: 'jwt-token',
          refreshToken: 'refresh-token',
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => mockResponse,
      });

      const result = await apiClient.login({
        email: '<EMAIL>',
        password: 'password123',
      });

      expect(mockFetch).toHaveBeenCalledWith(
        'https://api.test.com/auth/login',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-api-key',
          }),
          body: JSON.stringify({
            email: '<EMAIL>',
            password: 'password123',
          }),
        })
      );

      expect(result).toEqual(mockResponse.data);
    });

    it('should register new user', async () => {
      const mockUser = {
        id: '123',
        email: '<EMAIL>',
        fullName: 'New User',
        role: 'client',
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ success: true, data: mockUser }),
      });

      const result = await apiClient.register({
        email: '<EMAIL>',
        password: 'password123',
        fullName: 'New User',
        companyName: 'Test Company',
      });

      expect(result).toEqual(mockUser);
    });
  });

  describe('Bot Management', () => {
    it('should create a new bot', async () => {
      const mockBot = {
        id: 'bot-123',
        name: 'Test Bot',
        description: 'A test bot',
        isActive: true,
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ success: true, data: mockBot }),
      });

      const result = await apiClient.createBot({
        name: 'Test Bot',
        description: 'A test bot',
        settings: {
          primaryColor: '#4f46e5',
          greetingMessage: 'Hello!',
        },
      });

      expect(result).toEqual(mockBot);
    });

    it('should get bots list', async () => {
      const mockBots = [
        { id: 'bot-1', name: 'Bot 1' },
        { id: 'bot-2', name: 'Bot 2' },
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ success: true, data: mockBots }),
      });

      const result = await apiClient.getBots();

      expect(result.data).toEqual(mockBots);
    });

    it('should update bot settings', async () => {
      const mockUpdatedBot = {
        id: 'bot-123',
        name: 'Updated Bot',
        description: 'Updated description',
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ success: true, data: mockUpdatedBot }),
      });

      const result = await apiClient.updateBot('bot-123', {
        name: 'Updated Bot',
        description: 'Updated description',
      });

      expect(result).toEqual(mockUpdatedBot);
    });
  });

  describe('Knowledge Base Operations', () => {
    it('should upload document', async () => {
      const mockDocument = {
        id: 'doc-123',
        title: 'Test Document',
        processingStatus: 'processing',
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ success: true, data: mockDocument }),
      });

      const mockFile = new File(['content'], 'test.pdf', { type: 'application/pdf' });
      const result = await apiClient.uploadDocument('bot-123', {
        file: mockFile,
        title: 'Test Document',
        description: 'A test document',
      });

      expect(result.document).toEqual(mockDocument);
    });

    it('should get documents list', async () => {
      const mockDocuments = [
        { id: 'doc-1', title: 'Document 1' },
        { id: 'doc-2', title: 'Document 2' },
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ success: true, data: mockDocuments }),
      });

      const result = await apiClient.getDocuments('bot-123');

      expect(result.data).toEqual(mockDocuments);
    });

    it('should delete document', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 204,
        headers: new Headers(),
        text: async () => '',
      });

      await expect(apiClient.deleteKnowledgeDocument('doc-123')).resolves.not.toThrow();

      expect(mockFetch).toHaveBeenCalledWith(
        'https://api.test.com/knowledge/documents/doc-123',
        expect.objectContaining({
          method: 'DELETE',
        })
      );
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors correctly', async () => {
      const errorResponse = {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid input data',
          details: { field: 'email' },
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => errorResponse,
      });

      await expect(apiClient.login({
        email: 'invalid-email',
        password: 'password',
      })).rejects.toThrow(APIError);

      try {
        await apiClient.login({
          email: 'invalid-email',
          password: 'password',
        });
      } catch (error) {
        expect(isAPIError(error)).toBe(true);
        if (isAPIError(error)) {
          expect(error.code).toBe('VALIDATION_ERROR');
          expect(error.message).toBe('Invalid input data');
          expect(error.details).toEqual({ field: 'email' });
        }
      }
    });

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(apiClient.getBots()).rejects.toThrow('Network error');
    });

    it('should handle non-JSON responses', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        headers: new Headers({ 'content-type': 'text/plain' }),
        text: async () => 'Internal Server Error',
        json: async () => { throw new Error('Not JSON'); },
      });

      await expect(apiClient.getBots()).rejects.toThrow(APIError);
    });
  });

  describe('Rate Limiting', () => {
    it('should handle rate limit responses', async () => {
      const rateLimitResponse = {
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Too many requests',
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 429,
        headers: new Headers({
          'content-type': 'application/json',
          'x-ratelimit-limit': '100',
          'x-ratelimit-remaining': '0',
          'x-ratelimit-reset': '1640995200',
        }),
        json: async () => rateLimitResponse,
      });

      try {
        await apiClient.getBots();
      } catch (error) {
        expect(isAPIError(error)).toBe(true);
        if (isAPIError(error)) {
          expect(error.code).toBe('RATE_LIMIT_EXCEEDED');
        }
      }
    });
  });

  describe('Request Configuration', () => {
    it('should include custom headers', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ success: true, data: [] }),
      });

      await apiClient.getBots();

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-api-key',
            'Content-Type': 'application/json',
            'User-Agent': expect.stringContaining('WebtonAPIClient'),
          }),
        })
      );
    });

    it('should handle query parameters', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ 'content-type': 'application/json' }),
        json: async () => ({ success: true, data: [] }),
      });

      await apiClient.getBots({ page: 1, limit: 10 });

      expect(mockFetch).toHaveBeenCalledWith(
        'https://api.test.com/bots?page=1&limit=10',
        expect.any(Object)
      );
    });
  });
});

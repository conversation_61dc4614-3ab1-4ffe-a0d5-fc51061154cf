# Backend Integration Plan

> **Comprehensive Backend Architecture** - API endpoints, database schema, authentication flow, and integration patterns for the Webton AI Chatbots Platform.

## 🎯 Integration Overview

### Current State
- **Frontend**: React + TypeScript with mock data and local state
- **Widget**: Standalone component with configuration context
- **Data Flow**: Local state management with no persistence
- **Authentication**: None (development mode)

### Target State
- **Backend**: Supabase PostgreSQL + Edge Functions
- **Authentication**: Supabase Auth with JWT tokens
- **Real-time**: WebSocket connections for live chat
- **AI Integration**: OpenAI API + Qdrant vector database
- **File Storage**: Supabase Storage for knowledge documents

## 🗄️ Database Schema Design

### Core Tables

#### 1. Users (Authentication & Profiles)
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  full_name <PERSON><PERSON><PERSON><PERSON>(255),
  company_name VA<PERSON>HA<PERSON>(255),
  plan_type VARCHAR(50) DEFAULT 'free',
  api_key VARCHAR(255) UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_api_key ON users(api_key);
```

#### 2. Bots (Chatbot Configurations)
```sql
CREATE TABLE bots (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  type VARCHAR(50) NOT NULL, -- 'faq', 'lead_generation'
  status VARCHAR(50) DEFAULT 'active', -- 'active', 'inactive', 'training'
  config JSONB NOT NULL DEFAULT '{}',
  knowledge_base_id UUID,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_bots_user_id ON bots(user_id);
CREATE INDEX idx_bots_status ON bots(status);
CREATE INDEX idx_bots_type ON bots(type);
```

#### 3. Conversations (Chat Sessions)
```sql
CREATE TABLE conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  bot_id UUID REFERENCES bots(id) ON DELETE CASCADE,
  session_id VARCHAR(255) NOT NULL,
  user_identifier VARCHAR(255), -- IP, user ID, or anonymous ID
  status VARCHAR(50) DEFAULT 'active', -- 'active', 'ended', 'escalated'
  metadata JSONB DEFAULT '{}',
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ended_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_conversations_bot_id ON conversations(bot_id);
CREATE INDEX idx_conversations_session_id ON conversations(session_id);
CREATE INDEX idx_conversations_status ON conversations(status);
CREATE INDEX idx_conversations_started_at ON conversations(started_at);
```

#### 4. Messages (Chat Messages)
```sql
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  sender VARCHAR(50) NOT NULL, -- 'user', 'bot', 'system'
  message_type VARCHAR(50) DEFAULT 'text', -- 'text', 'image', 'file', 'system'
  metadata JSONB DEFAULT '{}',
  ai_response_time INTEGER, -- milliseconds for bot responses
  feedback_score INTEGER, -- 1-5 rating from user
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_messages_sender ON messages(sender);
CREATE INDEX idx_messages_created_at ON messages(created_at);
```

#### 5. Knowledge Base (Document Management)
```sql
CREATE TABLE knowledge_documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  bot_id UUID REFERENCES bots(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  content TEXT,
  file_path VARCHAR(500),
  file_type VARCHAR(50),
  file_size INTEGER,
  processing_status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
  embedding_status VARCHAR(50) DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_knowledge_documents_bot_id ON knowledge_documents(bot_id);
CREATE INDEX idx_knowledge_documents_status ON knowledge_documents(processing_status);
```

#### 6. Analytics (Performance Metrics)
```sql
CREATE TABLE analytics_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  bot_id UUID REFERENCES bots(id) ON DELETE CASCADE,
  event_type VARCHAR(100) NOT NULL, -- 'conversation_started', 'message_sent', 'lead_captured'
  event_data JSONB DEFAULT '{}',
  user_identifier VARCHAR(255),
  session_id VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_analytics_events_bot_id ON analytics_events(bot_id);
CREATE INDEX idx_analytics_events_type ON analytics_events(event_type);
CREATE INDEX idx_analytics_events_created_at ON analytics_events(created_at);
```

### Vector Embeddings (Qdrant Integration)
```sql
-- Store embedding metadata in PostgreSQL
CREATE TABLE document_embeddings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID REFERENCES knowledge_documents(id) ON DELETE CASCADE,
  chunk_index INTEGER NOT NULL,
  chunk_text TEXT NOT NULL,
  qdrant_point_id VARCHAR(255) UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_document_embeddings_document_id ON document_embeddings(document_id);
CREATE INDEX idx_document_embeddings_qdrant_id ON document_embeddings(qdrant_point_id);
```

## 🔌 API Endpoint Specifications

### Authentication Endpoints

#### POST /auth/login
```typescript
interface LoginRequest {
  email: string;
  password: string;
}

interface LoginResponse {
  user: User;
  access_token: string;
  refresh_token: string;
  expires_in: number;
}
```

#### POST /auth/register
```typescript
interface RegisterRequest {
  email: string;
  password: string;
  full_name: string;
  company_name?: string;
}
```

#### POST /auth/refresh
```typescript
interface RefreshRequest {
  refresh_token: string;
}
```

### Bot Management Endpoints

#### GET /bots
```typescript
interface GetBotsResponse {
  data: Bot[];
  pagination: {
    page: number;
    per_page: number;
    total: number;
    total_pages: number;
  };
}
```

#### POST /bots
```typescript
interface CreateBotRequest {
  name: string;
  type: 'faq' | 'lead_generation';
  config: BotConfig;
}

interface BotConfig {
  company_name: string;
  primary_color: string;
  secondary_color: string;
  greeting_message: string;
  position: 'bottom-right' | 'bottom-left';
  logo?: string;
  fallback_message?: string;
  escalation_enabled?: boolean;
}
```

#### PUT /bots/{bot_id}
```typescript
interface UpdateBotRequest {
  name?: string;
  config?: Partial<BotConfig>;
  status?: 'active' | 'inactive';
}
```

### Conversation Endpoints

#### POST /bots/{bot_id}/messages
```typescript
interface SendMessageRequest {
  message: string;
  session_id: string;
  user_id?: string;
  metadata?: Record<string, any>;
}

interface SendMessageResponse {
  response: string;
  message_id: string;
  session_id: string;
  response_time: number;
  confidence_score?: number;
  sources?: DocumentSource[];
}

interface DocumentSource {
  document_id: string;
  title: string;
  relevance_score: number;
  excerpt: string;
}
```

#### GET /bots/{bot_id}/conversations
```typescript
interface GetConversationsResponse {
  data: Conversation[];
  pagination: PaginationInfo;
}

interface Conversation {
  id: string;
  session_id: string;
  status: 'active' | 'ended' | 'escalated';
  message_count: number;
  started_at: string;
  ended_at?: string;
  user_identifier?: string;
  last_message?: Message;
}
```

### Knowledge Base Endpoints

#### POST /bots/{bot_id}/knowledge/documents
```typescript
interface UploadDocumentRequest {
  file: File;
  title?: string;
  description?: string;
}

interface UploadDocumentResponse {
  document_id: string;
  title: string;
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
  estimated_processing_time: number;
}
```

#### GET /bots/{bot_id}/knowledge/documents
```typescript
interface GetDocumentsResponse {
  data: KnowledgeDocument[];
  pagination: PaginationInfo;
}

interface KnowledgeDocument {
  id: string;
  title: string;
  file_type: string;
  file_size: number;
  processing_status: string;
  embedding_status: string;
  created_at: string;
  updated_at: string;
}
```

### Analytics Endpoints

#### GET /bots/{bot_id}/analytics
```typescript
interface GetAnalyticsRequest {
  start_date: string;
  end_date: string;
  metrics?: string[]; // ['conversations', 'messages', 'satisfaction', 'response_time']
  granularity?: 'hour' | 'day' | 'week' | 'month';
}

interface GetAnalyticsResponse {
  period: {
    start_date: string;
    end_date: string;
  };
  metrics: {
    total_conversations: number;
    total_messages: number;
    unique_users: number;
    average_response_time: number;
    satisfaction_score: number;
    resolution_rate: number;
  };
  time_series?: TimeSeriesData[];
}

interface TimeSeriesData {
  timestamp: string;
  conversations: number;
  messages: number;
  response_time: number;
}
```

## 🔐 Authentication Flow

### JWT Token Structure
```typescript
interface JWTPayload {
  sub: string; // user ID
  email: string;
  plan_type: string;
  iat: number;
  exp: number;
  aud: string;
  iss: string;
}
```

### API Key Authentication
```typescript
interface APIKeyPayload {
  user_id: string;
  key_id: string;
  permissions: string[];
  rate_limit: {
    requests_per_minute: number;
    requests_per_hour: number;
  };
}
```

### Permission Levels
```typescript
type Permission = 
  | 'bots:read'
  | 'bots:write'
  | 'conversations:read'
  | 'conversations:write'
  | 'knowledge:read'
  | 'knowledge:write'
  | 'analytics:read'
  | 'webhooks:write';
```

## 🔄 Real-time Integration

### WebSocket Events
```typescript
// Client to Server
interface ClientEvents {
  'join_conversation': { session_id: string; bot_id: string };
  'send_message': { message: string; session_id: string };
  'typing_start': { session_id: string };
  'typing_stop': { session_id: string };
}

// Server to Client
interface ServerEvents {
  'message_received': { message: Message; session_id: string };
  'bot_typing': { session_id: string };
  'conversation_ended': { session_id: string; reason: string };
  'error': { code: string; message: string };
}
```

---

*Next: Implementation of TypeScript types and API service layer*

*Last Updated: 2025-07-03*

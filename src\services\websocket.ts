/**
 * WebSocket Service
 * 
 * Real-time communication service for live chat functionality.
 * Handles connection management, message routing, and event handling.
 */

import {
  WebSocketMessage,
  JoinConversationPayload,
  SendMessagePayload,
  TypingPayload,
  MessageReceivedPayload,
  ConversationEndedPayload,
} from '../types/api';

export type WebSocketEventType = 
  | 'join_conversation'
  | 'leave_conversation'
  | 'send_message'
  | 'typing_start'
  | 'typing_stop'
  | 'message_received'
  | 'bot_typing'
  | 'conversation_ended'
  | 'error'
  | 'connected'
  | 'disconnected'
  | 'reconnecting';

export type WebSocketEventHandler<T = any> = (payload: T) => void;

export interface WebSocketConfig {
  url: string;
  accessToken?: string;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  heartbeatInterval?: number;
  debug?: boolean;
}

/**
 * WebSocket client for real-time chat communication.
 */
export class WebSocketClient {
  private ws: WebSocket | null = null;
  private config: WebSocketConfig;
  private eventHandlers: Map<WebSocketEventType, Set<WebSocketEventHandler>> = new Map();
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private isManuallyDisconnected = false;

  constructor(config: WebSocketConfig) {
    this.config = {
      reconnectInterval: 5000,
      maxReconnectAttempts: 5,
      heartbeatInterval: 30000,
      debug: false,
      ...config,
    };
  }

  /**
   * Connect to the WebSocket server.
   */
  async connect(): Promise<void> {
    if (this.ws?.readyState === WebSocket.OPEN || this.isConnecting) {
      return;
    }

    this.isConnecting = true;
    this.isManuallyDisconnected = false;

    try {
      const url = new URL(this.config.url);
      if (this.config.accessToken) {
        url.searchParams.set('token', this.config.accessToken);
      }

      this.ws = new WebSocket(url.toString());
      this.setupEventListeners();

      // Wait for connection to open
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('WebSocket connection timeout'));
        }, 10000);

        this.ws!.onopen = () => {
          clearTimeout(timeout);
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          this.emit('connected', {});
          resolve();
        };

        this.ws!.onerror = (error) => {
          clearTimeout(timeout);
          this.isConnecting = false;
          reject(error);
        };
      });
    } catch (error) {
      this.isConnecting = false;
      this.log('Connection failed:', error);
      throw error;
    }
  }

  /**
   * Disconnect from the WebSocket server.
   */
  disconnect(): void {
    this.isManuallyDisconnected = true;
    this.clearReconnectTimer();
    this.clearHeartbeatTimer();

    if (this.ws) {
      this.ws.close(1000, 'Manual disconnect');
      this.ws = null;
    }

    this.emit('disconnected', { manual: true });
  }

  /**
   * Check if the WebSocket is connected.
   */
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  /**
   * Send a message through the WebSocket.
   */
  send<T>(type: WebSocketEventType, payload: T): void {
    if (!this.isConnected()) {
      this.log('Cannot send message: WebSocket not connected');
      return;
    }

    const message: WebSocketMessage<T> = {
      type,
      payload,
      timestamp: new Date().toISOString(),
    };

    this.ws!.send(JSON.stringify(message));
    this.log('Sent message:', message);
  }

  /**
   * Add an event listener.
   */
  on<T>(event: WebSocketEventType, handler: WebSocketEventHandler<T>): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, new Set());
    }
    this.eventHandlers.get(event)!.add(handler);
  }

  /**
   * Remove an event listener.
   */
  off<T>(event: WebSocketEventType, handler: WebSocketEventHandler<T>): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.delete(handler);
    }
  }

  /**
   * Remove all event listeners for an event type.
   */
  removeAllListeners(event?: WebSocketEventType): void {
    if (event) {
      this.eventHandlers.delete(event);
    } else {
      this.eventHandlers.clear();
    }
  }

  /**
   * Join a conversation room.
   */
  joinConversation(sessionId: string, botId: string): void {
    this.send<JoinConversationPayload>('join_conversation', {
      session_id: sessionId,
      bot_id: botId,
    });
  }

  /**
   * Leave a conversation room.
   */
  leaveConversation(sessionId: string): void {
    this.send('leave_conversation', { session_id: sessionId });
  }

  /**
   * Send a chat message.
   */
  sendMessage(message: string, sessionId: string): void {
    this.send<SendMessagePayload>('send_message', {
      message,
      session_id: sessionId,
    });
  }

  /**
   * Indicate that the user is typing.
   */
  startTyping(sessionId: string): void {
    this.send<TypingPayload>('typing_start', { session_id: sessionId });
  }

  /**
   * Indicate that the user stopped typing.
   */
  stopTyping(sessionId: string): void {
    this.send<TypingPayload>('typing_stop', { session_id: sessionId });
  }

  /**
   * Set up WebSocket event listeners.
   */
  private setupEventListeners(): void {
    if (!this.ws) return;

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        this.log('Received message:', message);
        this.emit(message.type as WebSocketEventType, message.payload);
      } catch (error) {
        this.log('Failed to parse WebSocket message:', error);
      }
    };

    this.ws.onclose = (event) => {
      this.log('WebSocket closed:', event.code, event.reason);
      this.clearHeartbeatTimer();
      
      if (!this.isManuallyDisconnected && this.shouldReconnect()) {
        this.scheduleReconnect();
      }

      this.emit('disconnected', {
        code: event.code,
        reason: event.reason,
        manual: this.isManuallyDisconnected,
      });
    };

    this.ws.onerror = (error) => {
      this.log('WebSocket error:', error);
      this.emit('error', { error });
    };
  }

  /**
   * Emit an event to all registered handlers.
   */
  private emit<T>(event: WebSocketEventType, payload: T): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(payload);
        } catch (error) {
          this.log('Error in event handler:', error);
        }
      });
    }
  }

  /**
   * Check if we should attempt to reconnect.
   */
  private shouldReconnect(): boolean {
    return (
      !this.isManuallyDisconnected &&
      this.reconnectAttempts < (this.config.maxReconnectAttempts || 5)
    );
  }

  /**
   * Schedule a reconnection attempt.
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimer) return;

    this.reconnectAttempts++;
    const delay = this.config.reconnectInterval! * Math.pow(2, this.reconnectAttempts - 1);

    this.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
    this.emit('reconnecting', { attempt: this.reconnectAttempts, delay });

    this.reconnectTimer = setTimeout(async () => {
      this.reconnectTimer = null;
      try {
        await this.connect();
      } catch (error) {
        this.log('Reconnect failed:', error);
        if (this.shouldReconnect()) {
          this.scheduleReconnect();
        }
      }
    }, delay);
  }

  /**
   * Clear the reconnect timer.
   */
  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  /**
   * Start the heartbeat to keep the connection alive.
   */
  private startHeartbeat(): void {
    this.clearHeartbeatTimer();
    
    if (this.config.heartbeatInterval) {
      this.heartbeatTimer = setInterval(() => {
        if (this.isConnected()) {
          this.send('ping', {});
        }
      }, this.config.heartbeatInterval);
    }
  }

  /**
   * Clear the heartbeat timer.
   */
  private clearHeartbeatTimer(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * Log debug messages if debug mode is enabled.
   */
  private log(...args: any[]): void {
    if (this.config.debug) {
      console.log('[WebSocket]', ...args);
    }
  }
}

/**
 * Default WebSocket client instance.
 */
export const wsClient = new WebSocketClient({
  url: import.meta.env.VITE_WS_URL || 'ws://localhost:3000/ws',
  debug: import.meta.env.DEV,
});

/**
 * Hook for using WebSocket in React components.
 */
export function useWebSocket() {
  return wsClient;
}

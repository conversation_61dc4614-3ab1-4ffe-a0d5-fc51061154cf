/**
 * Satisfaction Chart Component
 * 
 * Displays user satisfaction metrics using pie charts and bar charts.
 * Shows satisfaction score distribution and trends over time.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  PieChart, 
  Pie, 
  Cell, 
  BarChart, 
  Bar,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
} from 'recharts';
import { Star, TrendingUp, Users, ThumbsUp } from 'lucide-react';
import { cn } from '@/lib/utils';

import { MockAnalyticsService } from '@/services/analytics-mock';

interface SatisfactionChartProps {
  botId: string;
  dateRange: { from: Date; to: Date };
  detailed?: boolean;
  className?: string;
}

interface SatisfactionData {
  rating: number;
  count: number;
  percentage: number;
}

/**
 * Custom tooltip for satisfaction charts
 */
function SatisfactionTooltip({ active, payload, label }: any) {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    
    return (
      <div className="bg-background border border-border rounded-lg shadow-lg p-3">
        <p className="font-medium text-sm mb-2">
          {data.rating} Star{data.rating !== 1 ? 's' : ''}
        </p>
        <div className="space-y-1 text-sm">
          <div className="flex items-center justify-between gap-4">
            <span className="text-muted-foreground">Count:</span>
            <span className="font-medium">{data.count.toLocaleString()}</span>
          </div>
          <div className="flex items-center justify-between gap-4">
            <span className="text-muted-foreground">Percentage:</span>
            <span className="font-medium">{data.percentage}%</span>
          </div>
        </div>
      </div>
    );
  }
  return null;
}

export function SatisfactionChart({ 
  botId, 
  dateRange, 
  detailed = false,
  className 
}: SatisfactionChartProps) {
  const [satisfactionData, setSatisfactionData] = useState<SatisfactionData[]>([]);
  const [loading, setLoading] = useState(true);
  const [chartType, setChartType] = useState<'pie' | 'bar'>('pie');

  // Color scheme for satisfaction ratings
  const SATISFACTION_COLORS = [
    '#ef4444', // 1 star - red
    '#f97316', // 2 stars - orange
    '#eab308', // 3 stars - yellow
    '#22c55e', // 4 stars - green
    '#3b82f6', // 5 stars - blue
  ];

  /**
   * Load satisfaction data
   */
  const loadSatisfactionData = async () => {
    try {
      setLoading(true);
      const data = await MockAnalyticsService.getSatisfactionBreakdown(
        botId,
        dateRange.from.toISOString(),
        dateRange.to.toISOString()
      );
      setSatisfactionData(data);
    } catch (error) {
      console.error('Failed to load satisfaction data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSatisfactionData();
  }, [botId, dateRange]);

  // Calculate summary statistics
  const totalRatings = satisfactionData.reduce((sum, d) => sum + d.count, 0);
  const averageRating = satisfactionData.reduce((sum, d) => sum + (d.rating * d.count), 0) / totalRatings || 0;
  const positiveRatings = satisfactionData.filter(d => d.rating >= 4).reduce((sum, d) => sum + d.count, 0);
  const positivePercentage = totalRatings > 0 ? (positiveRatings / totalRatings) * 100 : 0;

  /**
   * Render pie chart
   */
  const renderPieChart = () => (
    <PieChart>
      <Pie
        data={satisfactionData}
        cx="50%"
        cy="50%"
        labelLine={false}
        label={({ rating, percentage }) => `${rating}★ (${percentage}%)`}
        outerRadius={120}
        fill="#8884d8"
        dataKey="count"
      >
        {satisfactionData.map((entry, index) => (
          <Cell 
            key={`cell-${index}`} 
            fill={SATISFACTION_COLORS[entry.rating - 1]} 
          />
        ))}
      </Pie>
      <Tooltip content={<SatisfactionTooltip />} />
      <Legend />
    </PieChart>
  );

  /**
   * Render bar chart
   */
  const renderBarChart = () => (
    <BarChart data={satisfactionData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
      <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
      <XAxis 
        dataKey="rating" 
        tick={{ fontSize: 12 }}
        tickFormatter={(value) => `${value}★`}
      />
      <YAxis 
        tick={{ fontSize: 12 }}
        tickFormatter={(value) => value.toLocaleString()}
      />
      <Tooltip content={<SatisfactionTooltip />} />
      <Bar 
        dataKey="count" 
        fill="hsl(var(--primary))"
        radius={[4, 4, 0, 0]}
      >
        {satisfactionData.map((entry, index) => (
          <Cell 
            key={`cell-${index}`} 
            fill={SATISFACTION_COLORS[entry.rating - 1]} 
          />
        ))}
      </Bar>
    </BarChart>
  );

  if (loading) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5" />
            User Satisfaction
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-center justify-center">
            <div className="animate-pulse text-muted-foreground">Loading satisfaction data...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5" />
              User Satisfaction
            </CardTitle>
            <CardDescription>
              {detailed 
                ? "Detailed breakdown of user satisfaction ratings"
                : "Distribution of user feedback and ratings"
              }
            </CardDescription>
          </div>
          
          {detailed && (
            <div className="flex items-center gap-2">
              <Button
                variant={chartType === 'pie' ? "default" : "outline"}
                size="sm"
                onClick={() => setChartType('pie')}
              >
                Pie
              </Button>
              <Button
                variant={chartType === 'bar' ? "default" : "outline"}
                size="sm"
                onClick={() => setChartType('bar')}
              >
                Bar
              </Button>
            </div>
          )}
        </div>
        
        {/* Summary stats */}
        <div className="flex items-center gap-4 mt-4">
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              Avg: {averageRating.toFixed(1)}/5 ⭐
            </Badge>
            <Badge variant="outline" className="text-xs">
              {totalRatings.toLocaleString()} total ratings
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <Badge 
              variant={positivePercentage >= 80 ? "default" : positivePercentage >= 60 ? "secondary" : "destructive"} 
              className="text-xs"
            >
              {positivePercentage.toFixed(1)}% positive (4-5★)
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="h-[300px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            {chartType === 'pie' ? renderPieChart() : renderBarChart()}
          </ResponsiveContainer>
        </div>
        
        {detailed && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4 pt-4 border-t">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {satisfactionData.filter(d => d.rating === 5)[0]?.count.toLocaleString() || '0'}
              </div>
              <div className="text-xs text-muted-foreground">5-Star Ratings</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {satisfactionData.filter(d => d.rating === 4)[0]?.count.toLocaleString() || '0'}
              </div>
              <div className="text-xs text-muted-foreground">4-Star Ratings</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {satisfactionData.filter(d => d.rating === 3)[0]?.count.toLocaleString() || '0'}
              </div>
              <div className="text-xs text-muted-foreground">3-Star Ratings</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {satisfactionData.filter(d => d.rating <= 2).reduce((sum, d) => sum + d.count, 0).toLocaleString()}
              </div>
              <div className="text-xs text-muted-foreground">1-2 Star Ratings</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

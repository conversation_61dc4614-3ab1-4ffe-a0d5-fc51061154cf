/**
 * Metrics Overview Component
 * 
 * Displays key performance indicators (KPIs) in a grid layout with trend indicators.
 * Shows total conversations, messages, users, response time, satisfaction, and resolution rate.
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  MessageSquare, 
  Users, 
  Clock, 
  Star,
  TrendingUp,
  TrendingDown,
  ArrowUpRight,
  ArrowDownRight,
  Target,
  Activity,
} from 'lucide-react';
import { cn } from '@/lib/utils';

import { AnalyticsMetrics, TimeSeriesData } from '@/types/api';

interface MetricsOverviewProps {
  metrics: AnalyticsMetrics;
  timeSeriesData: TimeSeriesData[];
  dateRange: { from: Date; to: Date };
  className?: string;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  description: string;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
    period: string;
  };
  format?: 'number' | 'percentage' | 'time' | 'rating';
  className?: string;
}

/**
 * Individual metric card component
 */
function MetricCard({ 
  title, 
  value, 
  description, 
  icon, 
  trend, 
  format = 'number',
  className 
}: MetricCardProps) {
  /**
   * Format the metric value based on type
   */
  const formatValue = (val: string | number, fmt: string): string => {
    if (typeof val === 'string') return val;
    
    switch (fmt) {
      case 'percentage':
        return `${Math.round(val * 100)}%`;
      case 'time':
        return `${Math.round(val)}ms`;
      case 'rating':
        return `${val.toFixed(1)}/5`;
      case 'number':
      default:
        return val.toLocaleString();
    }
  };

  return (
    <Card className={cn("relative overflow-hidden", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <div className="text-muted-foreground">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {formatValue(value, format)}
        </div>
        <p className="text-xs text-muted-foreground mt-1">
          {description}
        </p>
        
        {trend && (
          <div className="flex items-center mt-2">
            <div className={cn(
              "flex items-center text-xs",
              trend.isPositive ? "text-green-600" : "text-red-600"
            )}>
              {trend.isPositive ? (
                <ArrowUpRight className="h-3 w-3 mr-1" />
              ) : (
                <ArrowDownRight className="h-3 w-3 mr-1" />
              )}
              <span className="font-medium">
                {trend.value.toFixed(1)}%
              </span>
            </div>
            <span className="text-xs text-muted-foreground ml-2">
              vs {trend.period}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export function MetricsOverview({ 
  metrics, 
  timeSeriesData, 
  dateRange,
  className 
}: MetricsOverviewProps) {
  /**
   * Calculate trend data by comparing current period with previous period
   */
  const calculateTrends = () => {
    if (timeSeriesData.length < 2) return {};
    
    const currentPeriodDays = Math.ceil(
      (dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    // Split data into current and previous periods
    const midPoint = Math.floor(timeSeriesData.length / 2);
    const currentPeriod = timeSeriesData.slice(midPoint);
    const previousPeriod = timeSeriesData.slice(0, midPoint);
    
    if (currentPeriod.length === 0 || previousPeriod.length === 0) return {};
    
    // Calculate averages for each period
    const currentAvg = {
      conversations: currentPeriod.reduce((sum, d) => sum + d.conversations, 0) / currentPeriod.length,
      messages: currentPeriod.reduce((sum, d) => sum + d.messages, 0) / currentPeriod.length,
      response_time: currentPeriod.reduce((sum, d) => sum + d.response_time, 0) / currentPeriod.length,
      satisfaction: currentPeriod.reduce((sum, d) => sum + (d.satisfaction_score || 0), 0) / currentPeriod.length,
    };
    
    const previousAvg = {
      conversations: previousPeriod.reduce((sum, d) => sum + d.conversations, 0) / previousPeriod.length,
      messages: previousPeriod.reduce((sum, d) => sum + d.messages, 0) / previousPeriod.length,
      response_time: previousPeriod.reduce((sum, d) => sum + d.response_time, 0) / previousPeriod.length,
      satisfaction: previousPeriod.reduce((sum, d) => sum + (d.satisfaction_score || 0), 0) / previousPeriod.length,
    };
    
    // Calculate percentage changes
    const calculateChange = (current: number, previous: number) => {
      if (previous === 0) return { value: 0, isPositive: true };
      const change = ((current - previous) / previous) * 100;
      return { value: Math.abs(change), isPositive: change >= 0 };
    };
    
    return {
      conversations: calculateChange(currentAvg.conversations, previousAvg.conversations),
      messages: calculateChange(currentAvg.messages, previousAvg.messages),
      response_time: calculateChange(currentAvg.response_time, previousAvg.response_time),
      satisfaction: calculateChange(currentAvg.satisfaction, previousAvg.satisfaction),
    };
  };

  const trends = calculateTrends();
  const periodText = `previous ${Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24))} days`;

  return (
    <div className={cn("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4", className)}>
      <MetricCard
        title="Total Conversations"
        value={metrics.total_conversations}
        description="Unique chat sessions started"
        icon={<MessageSquare className="h-4 w-4" />}
        trend={trends.conversations ? { ...trends.conversations, period: periodText } : undefined}
        format="number"
      />
      
      <MetricCard
        title="Total Messages"
        value={metrics.total_messages}
        description="Messages exchanged in all conversations"
        icon={<Activity className="h-4 w-4" />}
        trend={trends.messages ? { ...trends.messages, period: periodText } : undefined}
        format="number"
      />
      
      <MetricCard
        title="Unique Users"
        value={metrics.unique_users}
        description="Individual users who interacted"
        icon={<Users className="h-4 w-4" />}
        format="number"
      />
      
      <MetricCard
        title="Avg Response Time"
        value={metrics.average_response_time}
        description="Average bot response time"
        icon={<Clock className="h-4 w-4" />}
        trend={trends.response_time ? { 
          ...trends.response_time, 
          period: periodText,
          isPositive: !trends.response_time.isPositive // Lower response time is better
        } : undefined}
        format="time"
      />
      
      <MetricCard
        title="Satisfaction Score"
        value={metrics.satisfaction_score}
        description="Average user rating"
        icon={<Star className="h-4 w-4" />}
        trend={trends.satisfaction ? { ...trends.satisfaction, period: periodText } : undefined}
        format="rating"
      />
      
      <MetricCard
        title="Resolution Rate"
        value={metrics.resolution_rate}
        description="Conversations successfully resolved"
        icon={<Target className="h-4 w-4" />}
        format="percentage"
      />
    </div>
  );
}

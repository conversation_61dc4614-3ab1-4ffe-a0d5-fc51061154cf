/**
 * Conversation Chart Component
 * 
 * Displays time series data for conversations and messages using recharts.
 * Supports different granularities and detailed/summary views.
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { format, parseISO } from 'date-fns';
import { MessageSquare, Activity, TrendingUp, BarChart3 } from 'lucide-react';
import { cn } from '@/lib/utils';

import { TimeSeriesData } from '@/types/api';

interface ConversationChartProps {
  data: TimeSeriesData[];
  granularity: 'hour' | 'day' | 'week' | 'month';
  detailed?: boolean;
  className?: string;
}

/**
 * Custom tooltip component for charts
 */
function CustomTooltip({ active, payload, label }: any) {
  if (active && payload && payload.length) {
    const date = parseISO(label);
    
    return (
      <div className="bg-background border border-border rounded-lg shadow-lg p-3">
        <p className="font-medium text-sm mb-2">
          {format(date, 'MMM dd, yyyy HH:mm')}
        </p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center gap-2 text-sm">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-muted-foreground">{entry.name}:</span>
            <span className="font-medium">{entry.value.toLocaleString()}</span>
          </div>
        ))}
      </div>
    );
  }
  return null;
}

/**
 * Format X-axis labels based on granularity
 */
function formatXAxisLabel(tickItem: string, granularity: string): string {
  const date = parseISO(tickItem);
  
  switch (granularity) {
    case 'hour':
      return format(date, 'HH:mm');
    case 'day':
      return format(date, 'MMM dd');
    case 'week':
      return format(date, 'MMM dd');
    case 'month':
      return format(date, 'MMM yyyy');
    default:
      return format(date, 'MMM dd');
  }
}

export function ConversationChart({ 
  data, 
  granularity, 
  detailed = false,
  className 
}: ConversationChartProps) {
  const [chartType, setChartType] = useState<'line' | 'area' | 'bar'>('area');
  const [activeMetric, setActiveMetric] = useState<'conversations' | 'messages' | 'both'>('both');

  // Calculate summary statistics
  const totalConversations = data.reduce((sum, d) => sum + d.conversations, 0);
  const totalMessages = data.reduce((sum, d) => sum + d.messages, 0);
  const avgConversationsPerPeriod = data.length > 0 ? totalConversations / data.length : 0;
  const avgMessagesPerPeriod = data.length > 0 ? totalMessages / data.length : 0;

  // Find peak values
  const peakConversations = Math.max(...data.map(d => d.conversations));
  const peakMessages = Math.max(...data.map(d => d.messages));

  /**
   * Render chart based on selected type
   */
  const renderChart = () => {
    const commonProps = {
      data,
      margin: { top: 5, right: 30, left: 20, bottom: 5 },
    };

    const xAxisProps = {
      dataKey: 'timestamp',
      tickFormatter: (value: string) => formatXAxisLabel(value, granularity),
      tick: { fontSize: 12 },
    };

    const yAxisProps = {
      tick: { fontSize: 12 },
      tickFormatter: (value: number) => value.toLocaleString(),
    };

    switch (chartType) {
      case 'line':
        return (
          <LineChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis {...xAxisProps} />
            <YAxis {...yAxisProps} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            {(activeMetric === 'conversations' || activeMetric === 'both') && (
              <Line
                type="monotone"
                dataKey="conversations"
                stroke="hsl(var(--primary))"
                strokeWidth={2}
                dot={{ r: 4 }}
                name="Conversations"
              />
            )}
            {(activeMetric === 'messages' || activeMetric === 'both') && (
              <Line
                type="monotone"
                dataKey="messages"
                stroke="hsl(var(--secondary))"
                strokeWidth={2}
                dot={{ r: 4 }}
                name="Messages"
              />
            )}
          </LineChart>
        );

      case 'area':
        return (
          <AreaChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis {...xAxisProps} />
            <YAxis {...yAxisProps} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            {(activeMetric === 'conversations' || activeMetric === 'both') && (
              <Area
                type="monotone"
                dataKey="conversations"
                stackId="1"
                stroke="hsl(var(--primary))"
                fill="hsl(var(--primary))"
                fillOpacity={0.6}
                name="Conversations"
              />
            )}
            {(activeMetric === 'messages' || activeMetric === 'both') && (
              <Area
                type="monotone"
                dataKey="messages"
                stackId={activeMetric === 'both' ? "1" : "2"}
                stroke="hsl(var(--secondary))"
                fill="hsl(var(--secondary))"
                fillOpacity={0.6}
                name="Messages"
              />
            )}
          </AreaChart>
        );

      case 'bar':
        return (
          <BarChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis {...xAxisProps} />
            <YAxis {...yAxisProps} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            {(activeMetric === 'conversations' || activeMetric === 'both') && (
              <Bar
                dataKey="conversations"
                fill="hsl(var(--primary))"
                name="Conversations"
                radius={[2, 2, 0, 0]}
              />
            )}
            {(activeMetric === 'messages' || activeMetric === 'both') && (
              <Bar
                dataKey="messages"
                fill="hsl(var(--secondary))"
                name="Messages"
                radius={[2, 2, 0, 0]}
              />
            )}
          </BarChart>
        );

      default:
        return null;
    }
  };

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Conversation Trends
            </CardTitle>
            <CardDescription>
              {detailed 
                ? "Detailed conversation and message analytics over time"
                : "Overview of conversation activity and engagement"
              }
            </CardDescription>
          </div>
          
          {detailed && (
            <div className="flex items-center gap-2">
              {/* Chart type selector */}
              <div className="flex items-center gap-1">
                {[
                  { type: 'area' as const, icon: TrendingUp },
                  { type: 'line' as const, icon: Activity },
                  { type: 'bar' as const, icon: BarChart3 },
                ].map(({ type, icon: Icon }) => (
                  <Button
                    key={type}
                    variant={chartType === type ? "default" : "outline"}
                    size="sm"
                    onClick={() => setChartType(type)}
                  >
                    <Icon className="h-4 w-4" />
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>
        
        {/* Summary stats */}
        <div className="flex items-center gap-4 mt-4">
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              Total: {totalConversations.toLocaleString()} conversations
            </Badge>
            <Badge variant="outline" className="text-xs">
              Avg: {Math.round(avgConversationsPerPeriod)} per {granularity}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              Total: {totalMessages.toLocaleString()} messages
            </Badge>
            <Badge variant="outline" className="text-xs">
              Avg: {Math.round(avgMessagesPerPeriod)} per {granularity}
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {detailed && (
          <Tabs value={activeMetric} onValueChange={(value) => setActiveMetric(value as any)} className="mb-4">
            <TabsList>
              <TabsTrigger value="both">Both Metrics</TabsTrigger>
              <TabsTrigger value="conversations">Conversations Only</TabsTrigger>
              <TabsTrigger value="messages">Messages Only</TabsTrigger>
            </TabsList>
          </Tabs>
        )}
        
        <div className="h-[400px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            {renderChart()}
          </ResponsiveContainer>
        </div>
        
        {detailed && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4 pt-4 border-t">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {peakConversations.toLocaleString()}
              </div>
              <div className="text-xs text-muted-foreground">Peak Conversations</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-secondary">
                {peakMessages.toLocaleString()}
              </div>
              <div className="text-xs text-muted-foreground">Peak Messages</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {data.length > 0 ? (totalMessages / totalConversations).toFixed(1) : '0'}
              </div>
              <div className="text-xs text-muted-foreground">Msgs per Conversation</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {data.length}
              </div>
              <div className="text-xs text-muted-foreground">Data Points</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

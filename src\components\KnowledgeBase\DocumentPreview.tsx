/**
 * Document Preview Component
 * 
 * Provides a preview interface for knowledge base documents with content
 * display, metadata, and editing capabilities.
 */

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  FileText,
  Edit,
  Download,
  Clock,
  FileIcon,
  Save,
  X,
  AlertCircle,
  CheckCircle,
  Loader2,
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import type { Database } from '@/types/supabase';

type KnowledgeDocument = Database['public']['Tables']['knowledge_documents']['Row'];

interface DocumentPreviewProps {
  document: KnowledgeDocument | null;
  open: boolean;
  onClose: () => void;
  onEdit?: (document: KnowledgeDocument, content: string) => Promise<void>;
  onDownload?: (document: KnowledgeDocument) => void;
  className?: string;
}

export const DocumentPreview: React.FC<DocumentPreviewProps> = ({
  document,
  open,
  onClose,
  onEdit,
  onDownload,
  className,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  if (!document) return null;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'processing':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'ready':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const colors = {
      processing: 'bg-blue-100 text-blue-800',
      ready: 'bg-green-100 text-green-800',
      error: 'bg-red-100 text-red-800',
    };

    return (
      <Badge className={colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const formatFileSize = (bytes: number | null) => {
    if (!bytes) return 'Unknown';
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleEditStart = () => {
    setEditedContent(document.content || '');
    setIsEditing(true);
  };

  const handleEditCancel = () => {
    setIsEditing(false);
    setEditedContent('');
  };

  const handleEditSave = async () => {
    if (!onEdit) return;

    setIsSaving(true);
    try {
      await onEdit(document, editedContent);
      setIsEditing(false);
      setEditedContent('');
    } catch (error) {
      console.error('Failed to save document:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const renderContent = () => {
    if (!document.content) {
      return (
        <div className="text-center py-8 text-muted-foreground">
          <FileIcon className="mx-auto h-12 w-12 mb-4" />
          <p>No content available for preview</p>
          {document.processing_status === 'processing' && (
            <p className="text-sm mt-2">Document is still being processed...</p>
          )}
        </div>
      );
    }

    if (isEditing) {
      return (
        <div className="space-y-4">
          <Textarea
            value={editedContent}
            onChange={(e) => setEditedContent(e.target.value)}
            className="min-h-[400px] font-mono text-sm"
            placeholder="Document content..."
          />
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={handleEditCancel}
              disabled={isSaving}
            >
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button
              onClick={handleEditSave}
              disabled={isSaving}
            >
              {isSaving ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Save className="mr-2 h-4 w-4" />
              )}
              {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </div>
      );
    }

    return (
      <ScrollArea className="h-[400px]">
        <div className="whitespace-pre-wrap font-mono text-sm p-4 bg-muted/30 rounded-md">
          {document.content}
        </div>
      </ScrollArea>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FileText className="h-6 w-6" />
              <div>
                <DialogTitle className="text-xl">{document.title}</DialogTitle>
                <DialogDescription className="mt-1">
                  {document.description || 'No description available'}
                </DialogDescription>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {getStatusIcon(document.processing_status)}
              {getStatusBadge(document.processing_status)}
            </div>
          </div>
        </DialogHeader>

        <Tabs defaultValue="content" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="content">Content</TabsTrigger>
            <TabsTrigger value="metadata">Metadata</TabsTrigger>
          </TabsList>

          <TabsContent value="content" className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Document Content</h3>
              <div className="flex gap-2">
                {document.processing_status === 'ready' && onEdit && !isEditing && (
                  <Button variant="outline" size="sm" onClick={handleEditStart}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </Button>
                )}
                {onDownload && (
                  <Button variant="outline" size="sm" onClick={() => onDownload(document)}>
                    <Download className="mr-2 h-4 w-4" />
                    Download
                  </Button>
                )}
              </div>
            </div>
            {renderContent()}
          </TabsContent>

          <TabsContent value="metadata" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Document Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      File Type
                    </label>
                    <p className="text-sm">{document.file_type || 'Unknown'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      File Size
                    </label>
                    <p className="text-sm">{formatFileSize(document.file_size)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Processing Status
                    </label>
                    <div className="flex items-center gap-2 mt-1">
                      {getStatusIcon(document.processing_status)}
                      <span className="text-sm">{document.processing_status}</span>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Embedding Status
                    </label>
                    <div className="flex items-center gap-2 mt-1">
                      {getStatusIcon(document.embedding_status)}
                      <span className="text-sm">{document.embedding_status}</span>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Created
                    </label>
                    <p className="text-sm">
                      {formatDistanceToNow(new Date(document.created_at), { addSuffix: true })}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(document.created_at).toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Last Updated
                    </label>
                    <p className="text-sm">
                      {formatDistanceToNow(new Date(document.updated_at), { addSuffix: true })}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(document.updated_at).toLocaleString()}
                    </p>
                  </div>
                </div>

                {document.file_path && (
                  <>
                    <Separator />
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        File Path
                      </label>
                      <p className="text-sm font-mono bg-muted p-2 rounded mt-1">
                        {document.file_path}
                      </p>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

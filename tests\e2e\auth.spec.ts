/**
 * Authentication End-to-End Tests
 * 
 * Tests for complete authentication flows including login, logout,
 * protected routes, and role-based access control.
 */

import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
  });

  test('should display login form on initial visit', async ({ page }) => {
    // Should redirect to auth page or show login form
    await expect(page.locator('h1')).toContainText(/sign in|login|welcome/i);
    
    // Check for login form elements
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
  });

  test('should show validation errors for invalid login', async ({ page }) => {
    // Try to submit empty form
    await page.click('button[type="submit"]');
    
    // Should show validation errors
    await expect(page.locator('text=Email is required')).toBeVisible();
    await expect(page.locator('text=Password is required')).toBeVisible();
    
    // Try with invalid email format
    await page.fill('input[type="email"]', 'invalid-email');
    await page.fill('input[type="password"]', 'short');
    await page.click('button[type="submit"]');
    
    await expect(page.locator('text=Invalid email format')).toBeVisible();
    await expect(page.locator('text=Password must be at least 8 characters')).toBeVisible();
  });

  test('should handle login with mock credentials', async ({ page }) => {
    // Note: This test uses mock authentication since we don't have a real backend
    // In a real implementation, you would use test credentials
    
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    
    // Mock the authentication response
    await page.route('**/auth/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            user: { id: '123', email: '<EMAIL>' },
            token: 'mock-jwt-token',
          },
        }),
      });
    });
    
    await page.click('button[type="submit"]');
    
    // Should redirect to dashboard or show success
    await expect(page).toHaveURL(/dashboard|home/);
    await expect(page.locator('text=Welcome')).toBeVisible();
  });

  test('should switch between login and signup forms', async ({ page }) => {
    // Should start on login form
    await expect(page.locator('h1')).toContainText(/sign in|login/i);
    
    // Click signup link
    await page.click('text=Sign up');
    
    // Should switch to signup form
    await expect(page.locator('h1')).toContainText(/sign up|register/i);
    await expect(page.locator('input[placeholder*="Full name"]')).toBeVisible();
    await expect(page.locator('input[placeholder*="Company"]')).toBeVisible();
    
    // Click back to login
    await page.click('text=Sign in');
    
    // Should be back on login form
    await expect(page.locator('h1')).toContainText(/sign in|login/i);
  });

  test('should handle signup form validation', async ({ page }) => {
    // Navigate to signup form
    await page.click('text=Sign up');
    
    // Try to submit empty form
    await page.click('button[type="submit"]');
    
    // Should show validation errors
    await expect(page.locator('text=Full name is required')).toBeVisible();
    await expect(page.locator('text=Email is required')).toBeVisible();
    await expect(page.locator('text=Password is required')).toBeVisible();
  });

  test('should handle password reset flow', async ({ page }) => {
    // Click forgot password link
    await page.click('text=Forgot password');
    
    // Should show reset password form
    await expect(page.locator('h1')).toContainText(/reset password|forgot password/i);
    await expect(page.locator('input[type="email"]')).toBeVisible();
    
    // Submit with valid email
    await page.fill('input[type="email"]', '<EMAIL>');
    
    // Mock the reset password response
    await page.route('**/auth/reset-password', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          message: 'Password reset email sent',
        }),
      });
    });
    
    await page.click('button[type="submit"]');
    
    // Should show success message
    await expect(page.locator('text=Password reset email sent')).toBeVisible();
  });

  test('should protect dashboard routes when not authenticated', async ({ page }) => {
    // Try to access dashboard directly
    await page.goto('/dashboard');
    
    // Should redirect to login or show auth form
    await expect(page).toHaveURL(/auth|login/);
    await expect(page.locator('h1')).toContainText(/sign in|login/i);
  });

  test('should handle logout flow', async ({ page }) => {
    // First, mock login
    await page.route('**/auth/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            user: { id: '123', email: '<EMAIL>' },
            token: 'mock-jwt-token',
          },
        }),
      });
    });
    
    // Login
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // Should be on dashboard
    await expect(page).toHaveURL(/dashboard|home/);
    
    // Mock logout response
    await page.route('**/auth/logout', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true }),
      });
    });
    
    // Click logout (might be in a dropdown or menu)
    await page.click('[data-testid="user-menu"]').catch(() => {
      // Fallback if user menu not found
      return page.click('text=Logout');
    });
    
    await page.click('text=Logout');
    
    // Should redirect back to login
    await expect(page).toHaveURL(/auth|login/);
    await expect(page.locator('h1')).toContainText(/sign in|login/i);
  });

  test('should persist authentication state on page reload', async ({ page }) => {
    // Mock login
    await page.route('**/auth/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            user: { id: '123', email: '<EMAIL>' },
            token: 'mock-jwt-token',
          },
        }),
      });
    });
    
    // Login
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // Should be authenticated
    await expect(page).toHaveURL(/dashboard|home/);
    
    // Reload the page
    await page.reload();
    
    // Should still be authenticated (assuming session persistence)
    await expect(page).toHaveURL(/dashboard|home/);
    await expect(page.locator('text=Welcome')).toBeVisible();
  });

  test('should handle session expiration', async ({ page }) => {
    // Mock login
    await page.route('**/auth/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            user: { id: '123', email: '<EMAIL>' },
            token: 'mock-jwt-token',
          },
        }),
      });
    });
    
    // Login
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // Mock session expiration on next API call
    await page.route('**/api/**', async route => {
      await route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: { code: 'UNAUTHORIZED', message: 'Session expired' },
        }),
      });
    });
    
    // Try to access a protected resource
    await page.click('text=Analytics').catch(() => {
      // Fallback if analytics link not found
      return page.reload();
    });
    
    // Should redirect to login due to session expiration
    await expect(page).toHaveURL(/auth|login/);
  });
});

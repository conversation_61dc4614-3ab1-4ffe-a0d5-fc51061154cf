/**
 * Basic E2E Test Suite
 * 
 * Simple tests to verify Playwright E2E testing infrastructure is working correctly.
 */

import { test, expect } from '@playwright/test';

test.describe('Basic E2E Tests', () => {
  test('should load a basic page', async ({ page }) => {
    // Create a simple HTML page for testing
    await page.setContent(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Test Page</title>
        </head>
        <body>
          <h1>Welcome to Test Page</h1>
          <button id="test-button">Click Me</button>
          <div id="result"></div>
          <script>
            document.getElementById('test-button').addEventListener('click', () => {
              document.getElementById('result').textContent = 'Button clicked!';
            });
          </script>
        </body>
      </html>
    `);

    // Verify page title
    await expect(page).toHaveTitle('Test Page');

    // Verify heading is visible
    await expect(page.locator('h1')).toHaveText('Welcome to Test Page');

    // Test button interaction
    await page.click('#test-button');
    await expect(page.locator('#result')).toHaveText('Button clicked!');
  });

  test('should handle form interactions', async ({ page }) => {
    await page.setContent(`
      <!DOCTYPE html>
      <html>
        <body>
          <form id="test-form">
            <input type="text" id="name-input" placeholder="Enter your name" />
            <input type="email" id="email-input" placeholder="Enter your email" />
            <button type="submit">Submit</button>
          </form>
          <div id="form-result"></div>
          <script>
            document.getElementById('test-form').addEventListener('submit', (e) => {
              e.preventDefault();
              const name = document.getElementById('name-input').value;
              const email = document.getElementById('email-input').value;
              document.getElementById('form-result').textContent = \`Hello \${name} (\${email})\`;
            });
          </script>
        </body>
      </html>
    `);

    // Fill form fields
    await page.fill('#name-input', 'John Doe');
    await page.fill('#email-input', '<EMAIL>');

    // Submit form
    await page.click('button[type="submit"]');

    // Verify result
    await expect(page.locator('#form-result')).toHaveText('Hello John Doe (<EMAIL>)');
  });

  test('should support API mocking', async ({ page }) => {
    // Mock API response
    await page.route('**/api/test', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ message: 'Mocked response', success: true }),
      });
    });

    await page.setContent(`
      <!DOCTYPE html>
      <html>
        <body>
          <button id="api-button">Call API</button>
          <div id="api-result"></div>
          <script>
            document.getElementById('api-button').addEventListener('click', async () => {
              try {
                const response = await fetch('/api/test');
                const data = await response.json();
                document.getElementById('api-result').textContent = data.message;
              } catch (error) {
                document.getElementById('api-result').textContent = 'Error: ' + error.message;
              }
            });
          </script>
        </body>
      </html>
    `);

    // Click button to trigger API call
    await page.click('#api-button');

    // Verify mocked response
    await expect(page.locator('#api-result')).toHaveText('Mocked response');
  });
});

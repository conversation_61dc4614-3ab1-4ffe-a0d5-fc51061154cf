<testsuites id="" name="" tests="15" failures="5" skipped="0" errors="0" time="13.770253">
<testsuite name="basic.spec.ts" timestamp="2025-07-03T22:32:23.643Z" hostname="chromium" tests="3" failures="1" skipped="0" time="6.731" errors="0">
<testcase name="Basic E2E Tests › should load a basic page" classname="basic.spec.ts" time="0.453">
</testcase>
<testcase name="Basic E2E Tests › should handle form interactions" classname="basic.spec.ts" time="0.476">
</testcase>
<testcase name="Basic E2E Tests › should support API mocking" classname="basic.spec.ts" time="5.802">
<failure message="basic.spec.ts:76:3 should support API mocking" type="FAILURE">
<![CDATA[  [chromium] › basic.spec.ts:76:3 › Basic E2E Tests › should support API mocking ───────────────────

    Error: Timed out 5000ms waiting for expect(locator).toHaveText(expected)

    Locator: locator('#api-result')
    Expected string: "Mocked response"
    Received string: "Error: Failed to execute 'fetch' on 'Window': Failed to parse URL from /api/test"
    Call log:
      - Expect "toHaveText" with timeout 5000ms
      - waiting for locator('#api-result')
        9 × locator resolved to <div id="api-result">Error: Failed to execute 'fetch' on 'Window': Fai…</div>
          - unexpected value "Error: Failed to execute 'fetch' on 'Window': Failed to parse URL from /api/test"


      109 |
      110 |     // Verify mocked response
    > 111 |     await expect(page.locator('#api-result')).toHaveText('Mocked response');
          |                                               ^
      112 |   });
      113 | });
      114 |
        at D:\VSCode Projects\Webton Projects\Webton-Chatbots\tests\e2e\basic.spec.ts:111:47

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\basic-Basic-E2E-Tests-should-support-API-mocking-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\basic-Basic-E2E-Tests-should-support-API-mocking-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-Basic-E2E-Tests-should-support-API-mocking-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-Basic-E2E-Tests-should-support-API-mocking-chromium\test-failed-1.png]]

[[ATTACHMENT|basic-Basic-E2E-Tests-should-support-API-mocking-chromium\video.webm]]

[[ATTACHMENT|basic-Basic-E2E-Tests-should-support-API-mocking-chromium\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="basic.spec.ts" timestamp="2025-07-03T22:32:23.643Z" hostname="firefox" tests="3" failures="1" skipped="0" time="10.508" errors="0">
<testcase name="Basic E2E Tests › should load a basic page" classname="basic.spec.ts" time="1.96">
</testcase>
<testcase name="Basic E2E Tests › should handle form interactions" classname="basic.spec.ts" time="1.79">
</testcase>
<testcase name="Basic E2E Tests › should support API mocking" classname="basic.spec.ts" time="6.758">
<failure message="basic.spec.ts:76:3 should support API mocking" type="FAILURE">
<![CDATA[  [firefox] › basic.spec.ts:76:3 › Basic E2E Tests › should support API mocking ────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toHaveText(expected)

    Locator: locator('#api-result')
    Expected string: "Mocked response"
    Received string: "Error: Window.fetch: /api/test is not a valid URL."
    Call log:
      - Expect "toHaveText" with timeout 5000ms
      - waiting for locator('#api-result')
        9 × locator resolved to <div id="api-result">Error: Window.fetch: /api/test is not a valid URL.</div>
          - unexpected value "Error: Window.fetch: /api/test is not a valid URL."


      109 |
      110 |     // Verify mocked response
    > 111 |     await expect(page.locator('#api-result')).toHaveText('Mocked response');
          |                                               ^
      112 |   });
      113 | });
      114 |
        at D:\VSCode Projects\Webton Projects\Webton-Chatbots\tests\e2e\basic.spec.ts:111:47

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\basic-Basic-E2E-Tests-should-support-API-mocking-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\basic-Basic-E2E-Tests-should-support-API-mocking-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-Basic-E2E-Tests-should-support-API-mocking-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-Basic-E2E-Tests-should-support-API-mocking-firefox\test-failed-1.png]]

[[ATTACHMENT|basic-Basic-E2E-Tests-should-support-API-mocking-firefox\video.webm]]

[[ATTACHMENT|basic-Basic-E2E-Tests-should-support-API-mocking-firefox\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="basic.spec.ts" timestamp="2025-07-03T22:32:23.643Z" hostname="webkit" tests="3" failures="1" skipped="0" time="8.347" errors="0">
<testcase name="Basic E2E Tests › should load a basic page" classname="basic.spec.ts" time="1.212">
</testcase>
<testcase name="Basic E2E Tests › should handle form interactions" classname="basic.spec.ts" time="1.303">
</testcase>
<testcase name="Basic E2E Tests › should support API mocking" classname="basic.spec.ts" time="5.832">
<failure message="basic.spec.ts:76:3 should support API mocking" type="FAILURE">
<![CDATA[  [webkit] › basic.spec.ts:76:3 › Basic E2E Tests › should support API mocking ─────────────────────

    Error: Timed out 5000ms waiting for expect(locator).toHaveText(expected)

    Locator: locator('#api-result')
    Expected string: "Mocked response"
    Received string: "Error: URL is not valid or contains user credentials."
    Call log:
      - Expect "toHaveText" with timeout 5000ms
      - waiting for locator('#api-result')
        9 × locator resolved to <div id="api-result">Error: URL is not valid or contains user credenti…</div>
          - unexpected value "Error: URL is not valid or contains user credentials."


      109 |
      110 |     // Verify mocked response
    > 111 |     await expect(page.locator('#api-result')).toHaveText('Mocked response');
          |                                               ^
      112 |   });
      113 | });
      114 |
        at D:\VSCode Projects\Webton Projects\Webton-Chatbots\tests\e2e\basic.spec.ts:111:47

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\basic-Basic-E2E-Tests-should-support-API-mocking-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\basic-Basic-E2E-Tests-should-support-API-mocking-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-Basic-E2E-Tests-should-support-API-mocking-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-Basic-E2E-Tests-should-support-API-mocking-webkit\test-failed-1.png]]

[[ATTACHMENT|basic-Basic-E2E-Tests-should-support-API-mocking-webkit\video.webm]]

[[ATTACHMENT|basic-Basic-E2E-Tests-should-support-API-mocking-webkit\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="basic.spec.ts" timestamp="2025-07-03T22:32:23.643Z" hostname="Mobile Chrome" tests="3" failures="1" skipped="0" time="6.729" errors="0">
<testcase name="Basic E2E Tests › should load a basic page" classname="basic.spec.ts" time="0.434">
</testcase>
<testcase name="Basic E2E Tests › should handle form interactions" classname="basic.spec.ts" time="0.576">
</testcase>
<testcase name="Basic E2E Tests › should support API mocking" classname="basic.spec.ts" time="5.719">
<failure message="basic.spec.ts:76:3 should support API mocking" type="FAILURE">
<![CDATA[  [Mobile Chrome] › basic.spec.ts:76:3 › Basic E2E Tests › should support API mocking ──────────────

    Error: Timed out 5000ms waiting for expect(locator).toHaveText(expected)

    Locator: locator('#api-result')
    Expected string: "Mocked response"
    Received string: "Error: Failed to execute 'fetch' on 'Window': Failed to parse URL from /api/test"
    Call log:
      - Expect "toHaveText" with timeout 5000ms
      - waiting for locator('#api-result')
        9 × locator resolved to <div id="api-result">Error: Failed to execute 'fetch' on 'Window': Fai…</div>
          - unexpected value "Error: Failed to execute 'fetch' on 'Window': Failed to parse URL from /api/test"


      109 |
      110 |     // Verify mocked response
    > 111 |     await expect(page.locator('#api-result')).toHaveText('Mocked response');
          |                                               ^
      112 |   });
      113 | });
      114 |
        at D:\VSCode Projects\Webton Projects\Webton-Chatbots\tests\e2e\basic.spec.ts:111:47

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\basic-Basic-E2E-Tests-should-support-API-mocking-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\basic-Basic-E2E-Tests-should-support-API-mocking-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-Basic-E2E-Tests-should-support-API-mocking-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-Basic-E2E-Tests-should-support-API-mocking-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|basic-Basic-E2E-Tests-should-support-API-mocking-Mobile-Chrome\video.webm]]

[[ATTACHMENT|basic-Basic-E2E-Tests-should-support-API-mocking-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="basic.spec.ts" timestamp="2025-07-03T22:32:23.643Z" hostname="Mobile Safari" tests="3" failures="1" skipped="0" time="7.099" errors="0">
<testcase name="Basic E2E Tests › should load a basic page" classname="basic.spec.ts" time="0.834">
</testcase>
<testcase name="Basic E2E Tests › should handle form interactions" classname="basic.spec.ts" time="0.672">
</testcase>
<testcase name="Basic E2E Tests › should support API mocking" classname="basic.spec.ts" time="5.593">
<failure message="basic.spec.ts:76:3 should support API mocking" type="FAILURE">
<![CDATA[  [Mobile Safari] › basic.spec.ts:76:3 › Basic E2E Tests › should support API mocking ──────────────

    Error: Timed out 5000ms waiting for expect(locator).toHaveText(expected)

    Locator: locator('#api-result')
    Expected string: "Mocked response"
    Received string: "Error: URL is not valid or contains user credentials."
    Call log:
      - Expect "toHaveText" with timeout 5000ms
      - waiting for locator('#api-result')
        9 × locator resolved to <div id="api-result">Error: URL is not valid or contains user credenti…</div>
          - unexpected value "Error: URL is not valid or contains user credentials."


      109 |
      110 |     // Verify mocked response
    > 111 |     await expect(page.locator('#api-result')).toHaveText('Mocked response');
          |                                               ^
      112 |   });
      113 | });
      114 |
        at D:\VSCode Projects\Webton Projects\Webton-Chatbots\tests\e2e\basic.spec.ts:111:47

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\basic-Basic-E2E-Tests-should-support-API-mocking-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\basic-Basic-E2E-Tests-should-support-API-mocking-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\basic-Basic-E2E-Tests-should-support-API-mocking-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|basic-Basic-E2E-Tests-should-support-API-mocking-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|basic-Basic-E2E-Tests-should-support-API-mocking-Mobile-Safari\video.webm]]

[[ATTACHMENT|basic-Basic-E2E-Tests-should-support-API-mocking-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>
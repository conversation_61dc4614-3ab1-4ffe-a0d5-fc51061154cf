# Webton AI Chatbots Platform

> **AI-powered chatbot widgets for websites** - A comprehensive SaaS platform for creating, managing, and deploying intelligent chatbots that seamlessly integrate into any website.

[![Development Status](https://img.shields.io/badge/Status-Active%20Development-green)](https://github.com/webton/chatbots)
[![Tech Stack](https://img.shields.io/badge/Stack-React%20%7C%20TypeScript%20%7C%20Vite-blue)](#tech-stack)
[![License](https://img.shields.io/badge/License-Proprietary-red)](LICENSE)

## 🎯 Project Overview

Webton AI Chatbots is a modern SaaS platform that enables businesses to deploy intelligent, customizable chatbot widgets on their websites. The platform focuses on simplicity and effectiveness, offering two primary use cases:

- **FAQ Bots**: Automatically answer frequently asked questions using RAG (Retrieval Augmented Generation)
- **Lead Qualification Bots**: Engage visitors and collect qualified leads through intelligent conversations

### Key Features

- 🚀 **No-Code Setup**: Simple dashboard for bot configuration without technical expertise
- 🎨 **Full Customization**: Match your brand with custom colors, logos, and messaging
- 🧠 **AI-Powered**: Leverages OpenAI GPT models for natural conversations
- 📊 **Analytics Dashboard**: Track performance, conversations, and lead generation
- 🔧 **Easy Integration**: Single JavaScript snippet for website embedding
- 🌐 **Multi-Language Support**: Serve international customers in their language
- 📱 **Responsive Design**: Works seamlessly on desktop and mobile devices

## 🏗️ Architecture

The platform consists of three main components:

1. **Dashboard Web App** - React-based management interface for clients
2. **Embeddable Widget** - Lightweight JavaScript component for websites
3. **Cloud Backend** - API services, AI integration, and data management

## 📚 Documentation

- **[PLANNING.md](PLANNING.md)** - Project architecture, goals, and development guidelines
- **[TASK.md](TASK.md)** - Current sprint tasks and active development items
- **[TODO.md](TODO.md)** - Feature backlog and future roadmap
- **[CONTRIBUTING.md](CONTRIBUTING.md)** - Development workflow and coding standards
- **[docs/dev/](docs/dev/)** - Technical research and specifications

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and npm/yarn
- Modern web browser for development

### Installation

```bash
# Clone the repository
git clone https://github.com/webton/chatbots.git
cd chatbots

# Install dependencies
npm install

# Start development server
npm run dev
```

### Environment Setup

1. Copy the environment template:
```bash
cp .env.example .env
```

2. Configure your Supabase credentials in `.env`:
```bash
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
```

### Development Commands

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
npm run types:supabase # Generate Supabase types
```

## 🛠️ Tech Stack

### Frontend
- **React 18** with TypeScript for type safety
- **Vite** for fast development and building
- **Tailwind CSS** for styling and responsive design
- **ShadCN UI** for consistent, accessible components
- **Framer Motion** for smooth animations

### Backend (Planned)
- **Supabase** for database and authentication
- **OpenAI API** for language model integration
- **Qdrant** for vector database and embeddings
- **Vercel/Firebase** for serverless deployment

### Development Tools
- **Tempo Devtools** for component development
- **ESLint** for code quality
- **TypeScript** for type checking

## 🎨 Current Implementation

The project currently includes:

- ✅ **User Authentication** - Complete Supabase auth with role-based access control
- ✅ **Chat Widget Component** - Fully functional embeddable chat interface
- ✅ **Dashboard Interface** - Management UI with preview, analytics, and configuration
- ✅ **Widget Configuration** - Context-based configuration system
- ✅ **Analytics Dashboard** - Comprehensive analytics with charts and export functionality
- ✅ **Responsive Design** - Mobile-first approach with Tailwind CSS
- ✅ **Widget Embedding** - Production-ready standalone widget system

### 🔐 Authentication Features

- **User Registration & Login** - Email/password authentication with Supabase
- **Role-Based Access Control** - Admin, Client, and Viewer roles with granular permissions
- **Protected Routes** - Automatic route protection based on authentication status
- **Session Management** - Persistent sessions with automatic token refresh
- **User Profile Management** - Complete profile editing and account management
- **Password Reset** - Secure password reset flow via email
- ✅ **Component Library** - Comprehensive ShadCN UI integration

## 🔄 Development Status

**Current Phase**: Foundation Development
- Core widget functionality implemented
- Dashboard UI structure complete
- Configuration system operational
- Ready for backend integration

**Next Phase**: Backend Integration
- API development for bot management
- AI model integration
- User authentication system
- Database schema implementation

## 🤝 Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our development process, coding standards, and how to submit contributions.

## 📄 License

This project is proprietary software owned by Webton. All rights reserved.

---

**Built with ❤️ by the Webton Team**

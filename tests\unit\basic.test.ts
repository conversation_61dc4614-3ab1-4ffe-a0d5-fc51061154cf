/**
 * Basic Test Suite
 * 
 * Simple tests to verify testing infrastructure is working correctly.
 */

import { describe, it, expect, vi } from 'vitest';

describe('Testing Infrastructure', () => {
  it('should run basic tests', () => {
    expect(1 + 1).toBe(2);
  });

  it('should support mocking', () => {
    const mockFn = vi.fn();
    mockFn('test');
    expect(mockFn).toHaveBeenCalledWith('test');
  });

  it('should handle async operations', async () => {
    const promise = Promise.resolve('success');
    const result = await promise;
    expect(result).toBe('success');
  });

  it('should support fetch mocking', async () => {
    const mockFetch = vi.fn();
    global.fetch = mockFetch;

    mockFetch.mockResolvedValueOnce({
      ok: true,
      status: 200,
      json: async () => ({ message: 'success' }),
    });

    const response = await fetch('/api/test');
    const data = await response.json();

    expect(mockFetch).toHaveBeenCalledWith('/api/test');
    expect(data).toEqual({ message: 'success' });
  });
});

describe('Environment Setup', () => {
  it('should have access to DOM APIs', () => {
    const element = document.createElement('div');
    element.textContent = 'test';
    expect(element.textContent).toBe('test');
  });

  it('should support localStorage', () => {
    localStorage.setItem('test', 'value');
    expect(localStorage.getItem('test')).toBe('value');
    localStorage.removeItem('test');
  });
});

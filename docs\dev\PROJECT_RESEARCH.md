# Implementierung eines AI-Chatbot-Services für Webton-Klienten

## Überblick und Zielsetzung

Wir möchten einen **Webton AI-Chatbot-Service** entwickeln, mit dem sich für jeden Klienten individuell ein Chatbot erstellen lässt, der sich nahtlos als Widget in die jeweilige Website einbetten lässt. Dieses Widget erscheint typischerweise als Chat-Schaltfläche am Seitenrand und öffnet ein kleines Chatfenster, in dem Besucher Fragen stellen können und automatisierte Antworten erhalten. Solch ein Chatbot soll **rund um die Uhr verfügbar** sein und häufig gestellte Fragen sofort beantworten, wodurch Support-Teams entlastet und Besucher besser betreut werden. Gleichzeitig planen wir ein **Dashboard** für unsere Klienten, über das sie ihren Chatbot selbst verwalten, Inhalte anpassen und Nutzungsstatistiken einsehen können. Ziel ist es, einen Service ähnlich wie bestehende Plattformen (z. B. Botpress, GPTBots, Tidio etc.) bereitzustellen – jedoch **vereinfachter in der Bedienung**, sodass **keine aufwändige Flow-Erstellung** “Node für Node” nötig ist. Durch vordefinierte Templates und KI-gesteuerte Antworten soll die Einrichtung für Kunden ohne Programmierkenntnisse möglich sein.

## Kernfunktionen und Anwendungsfälle

Die ersten Anwendungsfälle, auf die wir uns fokussieren, sind klassische **FAQ-Bots** und **Lead-Qualifizierungs-Bots**:

* **FAQ-Bot:** Beantwortet automatisch häufig gestellte Fragen zu Produkten/Dienstleistungen des Klienten. Er greift dabei auf eine hinterlegte Wissensdatenbank (z. B. FAQs, Dokumentationen) zurück und formuliert Antworten in natürlicher Sprache. Durch *Retrieval Augmented Generation (RAG)* kann das Modell bei einer Nutzerfrage relevante kontextuelle Informationen aus den Dokumenten des Klienten abrufen und in die Antwort einfließen lassen. Dazu werden die Inhalte des Kunden (z. B. FAQ-Texte, Handbücher) in Vektor-Darstellungen (**Embeddings**) umgewandelt und in einer speziellen Datenbank (Vector-Database) gespeichert, die semantische Ähnlichkeiten findet. Der Chatbot verwendet diese Wissensbasis, um präzise und firmenspezifische Antworten zu liefern, anstatt nur generisches KI-Wissen zu nutzen. So können Website-Besucher rund um die Uhr verlässliche Antworten erhalten, ohne einen Mitarbeiter kontaktieren zu müssen – das verbessert den Support und entlastet das Team.
* **Lead-Qualifizierungs-Bot:** Dieser Chatbot begrüßt Website-Besucher und führt sie durch eine kurze Abfolge von Fragen, um deren Anliegen und Profil zu ermitteln (z. B. *„Womit können wir Ihnen helfen?“*, *„Suchen Sie nach Produkt X oder Y?“*, *„Dürfen wir Ihre Kontaktdaten für weitere Infos aufnehmen?“*). Ziel ist es, **automatisiert Leads zu erfassen und vorzuqualifizieren**, bevor ggf. ein Vertriebsteam übernimmt. Der Bot kann dabei freundliche, dynamische Gespräche führen und die Antworten der Nutzer auswerten. Wichtig ist, dass der Klient den Fragenkatalog und die Kriterien selbst festlegen kann (z. B. welche Fragen gestellt werden sollen, welche Antworten einen Lead als „heiß“ qualifizieren). Im Hintergrund kann die KI frei formulierte Antworten der Besucher interpretieren, aber die **Gesprächsstruktur** bleibt vorgegeben, um die gewünschten Daten zu sammeln. Hier können wir mit Templates arbeiten – z. B. eine Vorlage *„Lead-Gen Bot“*, in der nur noch die spezifischen Fragen und Texte angepasst werden müssen, anstatt jeden Dialogschritt manuell zu bauen. Auf diese Weise können auch weniger technisch versierte Kunden einen solchen Bot konfigurieren, ohne komplexe Entscheidungsbäume zu erstellen.

Zusätzlich zu diesen Hauptanwendungen könnten wir perspektivisch weitere nutzen: z. B. ein Bot zur Produktempfehlung im E-Commerce oder zur Terminvereinbarung. Auch eine **Hand-off-Funktion** ist sinnvoll – d. h. der Chatbot erkennt, wenn er nicht weiterkommt oder der Kunde einen Menschen sprechen möchte, und leitet dann an einen menschlichen Mitarbeiter oder ein Ticketsystem weiter. Dies steigert die Kundenzufriedenheit, da komplexe Fälle nicht in der Bot-Schleife hängenbleiben. Insgesamt sollen die Chatbots **mehrere Sprachen** unterstützen (für internationale Klienten) und in der **Markenstimme** des jeweiligen Unternehmens antworten können (Tonfall anpassbar). Diese Kernfunktionen sichern einen unmittelbaren Mehrwert: 24/7-Service, schnelle Beantwortung von Standardfragen, automatisierte Lead-Generierung und letztlich eine höhere Konversionsrate der Website-Besucher.

## Architektur: Aufbau des Chatbot-Services

Die Architektur gliedert sich grob in zwei Komponenten: **das Frontend-Widget auf der Kunden-Website** und **die Backend-Serverlogik (unsere Chatbot-Plattform)**.

**1. Chatbot-Widget (Frontend):** Für jeden Klienten generiert unser System ein Einbettungs-Snippet (JavaScript), das dieser in den Quellcode seiner Website einfügt. Dieses Snippet lädt das Chatbot-Widget – meist ein kleines Icon oder Button, der am unteren Bildschirmrand schwebt. Klickt der Besucher darauf, öffnet sich ein Chatfenster (eine kleine HTML/CSS-Komponente) über der Seite. Alle Nachrichten, die der Besucher eingibt, werden vom Widget an unser Backend übermittelt (per AJAX/WebSocket). Das Widget zeigt dann die Antwort der KI wieder im Chatfenster an. Wichtig ist hier, **Design-Anpassbarkeit** zu bieten: Im Dashboard soll der Kunde z. B. Farben, Logo, Begrüßungstext etc. seines Widgets einstellen können, damit es zum Look\&Feel der Website passt. Technisch kann das Widget als leichtgewichtiges JavaScript-Bundle gestaltet werden, das z. B. via CDN ausgeliefert wird – für den Kunden also nur ein Zeileneinbau. Beispielhaft nutzt Botpress einen ähnlichen Ansatz zur Web-Integration: ein Skript, das eine Chat-Blase in die Seite injiziert. Unser Widget benötigt keine eigene serverseitige Logik auf Kundenseite, sondern kommuniziert komplett mit unserem Backend.

&#x20;*Beispiel eines eingebetteten Chatbot-Widgets (hier auf der Payoneer-Website), das Kundenfragen direkt beantwortet und bei Bedarf an menschliche Agents weiterleiten kann.*

**2. Zentrale Backend-Plattform:** Auf unserer Seite läuft der eigentliche „Brain“ des Chatbots als Cloud-Service. Dieses Backend erhält die Anfragen vom Widget und orchestriert die Antwortfindung mittels KI. Hauptaufgaben des Backends sind:

* **Verständnis der Anfrage:** Zunächst wird die eingehende Nutzerfrage durch ein **Sprachmodell (LLM)** verarbeitet, um ihre Bedeutung zu erfassen. Hier kommt entweder ein externer KI-Dienst (z. B. OpenAI GPT) oder ein eigenes Modell zum Einsatz (mehr dazu unten). Bei rein konversationellen Bots kann man direkt das LLM antworten lassen; bei FAQ-Bots mit Wissensdatenbank folgt der nächste Schritt.
* **Wissensdatenbank-Abfrage (bei FAQ-Bot):** Mittels semantischer Suche werden aus der vom Klienten hinterlegten Wissensbasis passende Informationen zur Frage ermittelt. Wie erwähnt, nutzen wir dafür Embeddings und eine Vektor-Datenbank. Konkret: Die Frage des Nutzers wird in einen Vektor konvertiert (z. B. via OpenAI Embeddings oder einen eigenen Embedding-Modell) und es wird ein **Ähnlichkeitssuche** in der Datenbank durchgeführt. Die am besten passenden Dokumentenabschnitte oder FAQ-Antworten werden als Kontext zum LLM gegeben, damit die KI eine fundierte Antwort in den Worten des Unternehmens formulieren kann. Dieses *Retrieval-Augmented* Vorgehen stellt sicher, dass der Bot aktuelle und spezifische Informationen des Kundenunternehmens nutzt, anstatt nur auf allgemeines trainiertes Wissen zuzugreifen.
* **Dialog-Flow/Regeln (bei Lead-Bot):** Bei Lead-Qualifizierung oder anderen strukturierten Dialogen steuert das Backend den Ablauf: Es hält fest, welche Frage als Nächstes dran ist, welche Daten schon gesammelt wurden usw. Hier können wir einen einfachen State-Handler implementieren: Nach jeder Nutzerantwort entscheidet die Logik, ob weitere Fragen kommen oder ob die Konversation abgeschlossen ist (und die Daten als Lead gespeichert werden). Das LLM kann hierbei assistieren, indem es freie Nutzereingaben in strukturierte Antworten überführt (z. B. der Nutzer schreibt „Ich interessiere mich für Produkt X“ – die KI erkennt daraus, dass *Produkt X* relevant ist). Dennoch verbleibt die Sequenz der Fragen in unserer Kontrolle, um zielgerichtet zu bleiben. Diese Mischung aus regelbasiertem Ablauf und KI-Verständnis ermöglicht flexible, aber zielorientierte Gespräche.
* **Antwortgenerierung:** Auf Basis des Verstehens (und ggf. bereitgestellter Wissens-Snippets) generiert das LLM die Antwort formuliert aus. Hier definieren wir in der Prompt-Vorlage den gewünschten Ton (z. B. höflich, kompetent, Sie-Ansprache) und andere Parameter. Für verschiedene Bots können unterschiedliche Personas konfiguriert werden – etwa ein lockerer Ton für eine junge Marke vs. ein formeller Ton für eine Kanzlei. Solche **Identity Prompts** (Systemnachrichten) lassen sich im Dashboard pro Bot einstellen. Auch die maximale Antwortlänge oder Formatierungen können vorgegeben sein. Das LLM erzeugt also den Antworttext, den unser Backend dann ans Widget zurückschickt, damit es dem Nutzer angezeigt wird.
* **Logging & Analytics:** Alle Chats (Fragen und Antworten) werden serverseitig protokolliert, natürlich unter Einhaltung von Datenschutz (ggf. PII-Filterung). Diese Daten füttern das Analytics-Dashboard: Wir können Metriken berechnen wie Anzahl der Gespräche pro Tag, häufig gestellte Fragen, Durchschnittsdauer einer Session, Konversionsrate bei Leads etc. . Auch Feedback der Nutzer (Daumen hoch/runter für Antworten) könnte erfasst werden, um die Antwortqualität zu messen. Das Logging erlaubt außerdem, bei Bedarf manuell in Gespräche reinzuschauen – etwa um den Bot zu verbessern oder im Problemfall eine Konversation nachzuvollziehen.

**Multi-Tenancy & Mandantentrennung:** Da unser Service für *mehrere Klienten* gleichzeitig Bots hostet, müssen wir die Daten sauber isolieren. *Multi-Tenancy* bedeutet, dass eine einzige Software-Plattform mehrere Kunden (`Tenants`) bedient, wobei jeder Tenant nur seine eigenen Daten sieht und nutzt. In unserer Architektur lösen wir das z. B. so, dass jeder Bot bzw. jeder Kunde einen eigenen **API-Key oder ID** bekommt, die bei jeder Anfrage mitgesendet wird. Das Backend ordnet die Anfrage dann dem richtigen Kundenprojekt zu und lädt dessen Einstellungen (Wissensdatenbank, Prompt-Vorlagen etc.). Bei Verwendung einer zentralen Vektor-Datenbank kann man beispielsweise mit Tenant-IDs arbeiten – Qdrant etwa erlaubt es, alle Vektoren in einer Collection zu halten und via Metadaten-Filter nach Tenant zu trennen. Alternativ könnte man pro Kunde auch getrennte Datensammlungen führen. Wichtig ist: **Datenisolation und Sicherheit** haben höchste Priorität, damit keine Antworten mit fremden Inhalten vermischt werden. Ebenso achten wir auf Datenschutz – sensible Informationen der Endnutzer werden nicht unerlaubt zwischen Kunden geteilt, und wir können auf Wunsch der Klienten festlegen, dass ihre Chat-Daten z. B. *nicht* zum Training externer KI-Modelle verwendet werden (bei OpenAI lässt sich dies via API-Parameter steuern).

## Auswahl des KI-Modells und Kostenüberlegungen

Ein zentrales Thema ist, **welches Sprachmodell** die Chatbots antreibt, denn dies beeinflusst sowohl die Leistungsfähigkeit als auch die Kosten. Wir möchten flexibel sein und dem Klienten idealerweise die Wahl lassen, welches Modell für seinen Bot verwendet wird – z. B. OpenAI GPT-3.5 (günstiger, schnelle Antworten) vs. GPT-4 (teurer, aber leistungsfähiger), oder auch andere Anbieter (Claude von Anthropic, PaLM2 von Google etc.). Im Dashboard könnte der Kunde beim Erstellen des Bots das gewünschte Modell auswählen. Entsprechend werden die API-Aufrufe dann an das gewählte Modell geleitet. Eine **Pay-as-you-go Abrechnung** pro Nutzung erscheint sinnvoll: Wenn der Bot sehr aktiv genutzt wird (viele Anfragen), entstehen höhere KI-Kosten, die wir an den Kunden weiterberechnen (ggf. mit kleinem Aufschlag). So stellen wir sicher, dass wir profitabel arbeiten, da die Einnahmen die variablen Kosten übersteigen.

**OpenAI-Modelle vs. Open-Source:** Für den Start liegt der Fokus vermutlich auf **API-Modelle wie OpenAI**, weil diese **keine eigene Infrastruktur** erfordern und sofort *as-a-Service* verfügbar sind. Wir zahlen hier pro Anfrage (z. B. \$0.002 pro 1K Tokens bei GPT-3.5), was bei moderatem Volumen sehr günstig ist. Beispielrechnung: ~~5000 Anfragen pro Tag mit mittlerer Länge kosten etwa \$6.50 pro Tag (~~\$200 im Monat). Dieses nutzungsabhängige Modell skaliert linear – für einen kleinen Kunden mit wenigen Chats bleiben die Kosten minimal, für viele Chats entsprechend höher. **Der Vorteil**: Ohne Vorabinvestition an Hardware können wir sofort hohe Qualität bieten und zahlen nur, wenn der Service auch genutzt wird. **Der Nachteil**: Bei **sehr hohem Volumen** könnten die summierten API-Kosten beträchtlich werden. Studien zeigen, dass ab Millionen von Anfragen pro Tag eine eigene gehostete Lösung wirtschaftlicher sein *kann*, da die OpenAI-Gebühren dann die Serverkosten übersteigen. Zum Vergleich: Würde man ein Open-Source-Modell selbst hosten, fallen fixe Infrastrukturkosten an – z. B. auf AWS ca. \$5-6 pro Stunde für einen geeigneten GPU-Server (\~\$150 pro Tag), egal ob 1000 oder 1 Million Requests verarbeitet werden. Das heißt, bei sehr vielen Anfragen skaliert die eigene Lösung besser (nahezu Fixkosten), während die API-Kosten linear hochgehen.

Für den Anfang ist also **OpenAI (oder ähnliche Dienste)** die effizienteste Wahl, um schnell zu starten und Kosten direkt weiterzugeben. Wir könnten ein **Kontingent** in unsere Pakete einrechnen (z. B. im Grundpreis X Anfragen inklusive) und darüber hinaus pro 1000 Anfragen einen Betrag berechnen. Alternativ kann man dem Kunden auch erlauben, *sein eigenes API-Key* zu hinterlegen – dann würde der Kunde die KI-Kosten direkt tragen, und wir verlangen nur eine Plattform-Gebühr. Dies senkt unser Risiko, aber schmälert evtl. Umsatzchancen.

Langfristig könnten wir überlegen, **Open-Source-LLMs** einzubinden (z. B. Meta LLaMa 2, Mistral 7B/13B etc.), um unabhängiger von den großen API-Anbietern zu werden. Moderne Open-Source-Modelle erreichen schon beachtliche Qualität (Vicuna als Feintuning von LLaMa 13B schafft \~90% der ChatGPT-Qualität). Sie sind kostenlos lizenzierbar, aber man muss sie selbst hosten und skalieren. Das erfordert **Know-how in DevOps/ML** sowie die erwähnte Serverinfrastruktur. Ein Vorteil wäre, dass wir das Modell speziell anpassen könnten (Fine-Tuning auf Domänendaten eines Kunden, falls gewünscht) und *keine* Nutzerdaten an Dritte (OpenAI) abfließen. Für bestimmte datensensitive Kunden (z. B. im Bankensektor) ist ein selbstgehosteter Bot ein Pluspunkt in Sachen Datenschutz. Allerdings müssen wir genau rechnen: Bei wenigen Kunden lohnt sich der Aufwand nicht, weil die API-Nutzung günstiger ist als ein dedizierter Server. Daher ist der pragmatische Ansatz: **Start mit OpenAI** (schnelle Markteinführung, pay-per-use Kosten) und **bei Wachstum** evaluieren, ab wann ein eigener Hosting-Cluster rentabel ist. Gegebenenfalls kann man einen Mittelweg gehen und kleinere Anfragen weiter an OpenAI senden, aber z. B. längere Dokumentenanfragen über ein eigenes Modell abwickeln – je nachdem, was kosteneffizienter ist.

Zusätzlich zur Modellwahl beeinflusst auch die **Konversationslänge** die Kosten. Wir sollten in der Bot-Konfiguration Limits setzen können (maximale Antwortlänge, Anzahl Dialogschritte speichern etc.), um die Token-Nutzung zu kontrollieren. Ebenso kann man überlegen, ob wir *Zwischenergebnisse cachen*: Oft werden ähnliche Fragen wiederholt gestellt – eine einmal generierte Antwort könnte temporär gespeichert und bei identischer Frage wiederverwendet werden, anstatt das LLM erneut zu fragen. Solche Optimierungen könnten die API-Kosten weiter senken.

## Dashboard: Selbstbedienung und Anpassungen für Klienten

Ein **zentrales Feature** unseres Services ist das *Client-Dashboard*, über das die Kunden ihren Chatbot weitgehend selbst konfigurieren und verwalten können. Die Idee dahinter: Der Kunde soll seinen Bot **zielgenau auf die eigenen Bedürfnisse einstellen können, ohne für jede Änderung unser Team bemühen zu müssen** (obwohl wir natürlich Support anbieten). Dieses Dashboard wird als Web-Oberfläche implementiert (z. B. als Teil unserer Web-App) und bietet u.a. folgende Funktionen:

* **Bot-Erstellung und -Einstellungen:** Der Kunde kann einen neuen Bot anlegen, einen Namen vergeben und einen Anwendungsfall auswählen (z. B. *FAQ-Bot* oder *Lead-Bot* Template). Anschließend führt ein Formular durch die nötigen Einstellungen. Dazu zählt die Wahl des zugrundeliegenden KI-Modells (Dropdown mit GPT-3.5, GPT-4 etc.) sowie weitere Parameter wie maximale Antwortlänge, Sprache des Bots, Aktivierung von Funktionen (z. B. Hand-off zu menschlichem Support). Wichtig ist auch das Feld für den **„Identity Prompt“** – hier kann der Kunde in eigenen Worten beschreiben, welche Rolle der Bot einnimmt (etwa: *„Du bist der virtuelle Assistent der Firma XYZ und hilfst den Website-Besuchern freundlich bei Fragen zu unseren Software-Produkten.“*). Diese Angaben verwenden wir als System-Prompt, um den Ton und Kontext der KI-Antworten vorzugeben. Solche Einstellungen geben dem Klienten Kontrolle über die Persönlichkeit des Bots, ohne dass technische Details erforderlich sind.

* **Wissensbasis/Content-Management:** Für einen FAQ-Bot muss der Kunde seine Inhalte hochladen oder eingeben können. Im Dashboard wird es daher einen Bereich geben, in dem **Dokumente oder FAQ-Listen** hinterlegt werden. Möglich sind z. B. Uploads von PDF/DOC-Dateien, CSVs mit Frage-Antwort-Paaren, oder einfach freie Texteingabe von Frage+Antwort. Alternativ kann man eine URL zur bestehenden FAQ-Seite angeben, die wir dann crawlen und indexieren. Unsere Plattform verarbeitet diese Inhalte im Hintergrund: Texte werden in Vektoren umgerechnet und in die Wissensdatenbank (Vector Store) des jeweiligen Kunden eingespeist. Der Kunde sieht idealerweise eine Auflistung seiner hinterlegten Artikel/FAQs und kann sie bei Bedarf aktualisieren oder löschen. Für Änderungen (z. B. neue FAQ hinzufügen) stößt unser System dann eine Neuberechnung der Embeddings an. Dieser **No-Code-Ansatz** – einfach Dateien hochladen oder Textfelder ausfüllen – ermöglicht es den Kunden, den Wissenstand ihres Bots aktuell zu halten, **ohne selbst KI oder Datenbank-Expertise** zu benötigen.

* **Dialog-Flow-Konfiguration:** Speziell für Lead-Qualifizierung oder andere strukturierte Dialoge braucht der Kunde eine Möglichkeit, die Fragen und Logik festzulegen. Das könnte eine kleine **Formular-Sequenz** im Dashboard sein: Der Kunde gibt z. B. Frage 1 ein („Wie kann ich Ihnen helfen?“), definiert erwartete Angaben (z. B. Auswahl Produktkategorie, Freitext Problem), dann Frage 2 („Darf ich Ihren Namen erfahren?“) usw. Man könnte auch Bedingungen erlauben (wenn Antwort auf Frage 1 = X, dann stelle Frage Y). Allerdings sollte die Komplexität gering gehalten werden, evtl. beschränken wir uns zunächst auf lineare Fragefolgen. Alternativ bieten wir ein, zwei vordefinierte **Templates** an (Lead-Gen, Feedback, Terminbuchung) und der Kunde füllt nur die Textbausteine ein. Unser Ziel ist, **nicht die komplette Flexibilität eines Botpress** bereitzustellen, sondern häufige Anwendungsfälle stark zu vereinfachen. Für komplexere Anforderungen können wir immer noch Consulting anbieten, aber 80% der Use Cases sollen ohne Programmierung abbildbar sein.

* **Widget-Einbettung & Einstellungen:** Das Dashboard zeigt dem Kunden den JavaScript-Snippet an, den er in seine Website einbauen muss, um den Bot zu aktivieren. Evtl. integrieren wir sogar kleine Plugins für gängige CMS (WordPress, Shopify etc.) in Zukunft, damit Nicht-Techniker das einfacher einbinden können. Außerdem gibt es im Dashboard Optionen zur Darstellung des Widgets: z. B. Farbe des Chat-Icons, Avatar des Bots (Logo/Bild), Begrüßungstext, Position (rechts unten / links unten) usw\.. Diese Einstellungen werden live auf das Widget angewandt (das Script lädt sie via API). So kann der Kunde sicherstellen, dass der Chatbot optisch und sprachlich zum Unternehmen passt.

* **Analytics und Monitoring:** Ein großer Mehrwert ist das **Analytics-Dashboard**, in dem der Kunde Einblick in die Bot-Nutzung bekommt. Hier stellen wir grafisch und in Zahlen dar: **Gespräche pro Tag**, unique User, Konversionsrate (bei Lead-Bot, wie viele Leads erfasst), häufigste Nutzerfragen, durchschnittliche Antwortzeit, Feedback-Auswertungen usw\.. Eventuell kann man auch Chattranskripte durchsuchen, um zu sehen, was Nutzer gefragt haben und wo der Bot evtl. noch falsch liegt (wichtig für kontinuierliche Verbesserung). Diese Daten helfen dem Klienten, den ROI des Bots abzuschätzen (z. B. wie viele Supportanfragen abgefangen wurden, wie viele Leads generiert) und gezielt Inhalte nachzubessern. Zum Beispiel erkennt der Kunde vielleicht, dass häufig nach einem bestimmten Thema gefragt wird, das noch nicht in den FAQs steht – er kann diese Frage dann hinzufügen, damit der Bot künftig Antwort darauf hat. **Berichte** über die Performance (monatliche Zusammenfassung etc.) könnten wir ebenfalls anbieten. All das macht das Tool für Kunden greifbar und zeigt den Wert der Lösung transparent auf.

In Summe soll das Dashboard dem Klienten volle **Kontrolle** und **Transparenz** über seinen Chatbot geben. Viele moderne Bot-Plattformen werben genau damit: No-Code Erstellung, Knowledge-Upload, Widget Customizing und Analytics – wir werden diese Punkte also unbedingt erfüllen. Dies erhöht auch die Skalierbarkeit unseres Geschäfts, da wir nicht jeden Bot manuell konfigurieren müssen, sondern die Kunden selbst schnell experimentieren und neue Anwendungsfälle aufsetzen können.

## Betrieb und Infrastruktur

Bei der Umsetzung streben wir eine **möglichst kostengünstige und dennoch skalierbare Infrastruktur** an. Drei Ansätze stehen zur Debatte: Firebase, Vercel (Serverless) oder eigener Server – jeweils mit Vor- und Nachteilen:

* **Firebase**: Als Backend-as-a-Service bietet Firebase vieles von Haus aus, was wir brauchen: Datenbank (Firestore) zur Speicherung von Bot-Konfigurationen und Logs, Authentifizierung für das Dashboard, Cloud Functions für serverseitige Logik, und Hosting für die Dashboard-Webapp und evtl. das Widget-Skript. Ein Vorteil ist die einfache Integration und die nutzungsbasierte Abrechnung (kleine Projekte kosten nahezu nichts, es skaliert automatisch mit). Wir könnten z. B. die Nutzerfragen in Firestore loggen und mit Firebase Analytics auswerten. Die Cloud Functions könnten Anfragen vom Widget entgegennehmen, OpenAI API aufrufen und die Antwort zurückliefern. Latenz von Firebase (Google) ist in der Regel sehr gut, sodass die Antwortzeiten im Rahmen bleiben. Falls wir vektorbasierte Suche brauchen, könnten wir entweder einen Firebase-nahen Dienst (Firestore hat begrenzte Query-Fähigkeiten, aber es gibt keinen eingebauten Vektorstore). Denkbar wäre eine Kombination: Speicherung der Wissensdokumente in Firestore, bei Anfrage Berechnung der Embeddings on-the-fly und Suche via **FAISS** (Facebook AI Similarity Search) Bibliothek innerhalb der Function – allerdings könnte das bei großen Datenmengen langsam werden. Alternativ nutzen wir einen gehosteten Vektor-DB-Service (wie Pinecone, Qdrant Cloud) und rufen diesen aus der Function heraus auf. Firebase hat den Vorteil, dass wir wenig DevOps machen müssen. Nachteil: Logik in Functions darf nicht zu lange laufen (Time-Outs \~60s), große Modelle hosten geht dort nicht, und Firestore Kosten müssen beobachtet werden (viel Lese/Schreiboperationen könnten teuer werden). Für unseren MVP scheint Firebase aber ein schneller Weg zu sein.

* **Vercel (bzw. Serverless Functions)**: Vercel eignet sich hervorragend, um ein React/Next.js Dashboard zu hosten und gleichzeitig API-Routen bereitzustellen. Wir könnten das gesamte Dashboard als Next.js App bauen. Die Chatbot-API könnte als Vercel Serverless Function implementiert sein, die ähnlich wie bei Firebase die Anfragen verarbeitet. Auch hier zahlen wir nur pro Ausführung, was am Anfang günstig ist. Vercel hat Limits (z. B. max. 10s Execution Time für Edge Functions), aber Aufrufe an externe APIs (OpenAI) sind in <2s meist erledigt. Für persistentere Verbindungen (WebSocket Chat-Streams) müsste man schauen – eventuell Long Polling oder SSE (Server Sent Events) nutzen, da Vercel WebSockets tricky sein können. Vercel eignet sich gut für global verteiltes Hosting (CDN für Widget und Dashboard). Daten würden wir in diesem Szenario eventuell in einer separaten DB halten – etwa **Supabase** (eine PostgreSQL-basierte Backend-Option) oder wieder einen Vektorstore extern. Supabase könnte Bot-Konfig und Chat-Logs speichern; es bietet auch ein **pgvector**-Feature für Vektorsuche, was spannend wäre für unseren Use-Case (Postgres mit Vektor-Extension). Das wäre dann unsere eigene kleine Datenbank. Alternativ MongoDB Atlas (viele free-tier Optionen). Der Vorteil hier: etwas mehr Kontrolle als reines Firebase, und TypeScript/Node Code in Next.js lässt sich gut modular entwickeln. Das Deployment/Scaling übernimmt Vercel automatisch. Möglicher Nachteil: Bei sehr hohem Traffic könnten Serverless-Kosten steigen, oder wir stoßen an Kaltstart-Probleme. Aber bis dahin hätten wir zahlende Kunden, sodass es vertretbar ist.

* **Eigener Server**: Schließlich können wir einen eigenen Server oder VPS betreiben, auf dem das Backend (z. B. als Node.js/Express oder Python/FastAPI) läuft. Dort könnten wir auch einen Open-Source-LLM hosten, falls gewünscht. Ein *eigener Server* (oder Kubernetes-Cluster) gibt maximale Kontrolle – z. B. könnten wir einen langlebigen Prozess für Vektorsuche/Embedding vorhalten, um Latenz zu minimieren. Die Kosten beginnen hier bei einem festen Betrag pro Monat (je nach Leistung, ein kleiner Cloud-Server vielleicht 20–50€ mtl., mit GPU deutlich mehr). Für den Start eventuell Oversized, aber wenn wir einige Kunden drauf haben, kann es sich lohnen. Wir müssen dann allerdings selbst für Skalierung sorgen, wenn die Last steigt (Load Balancer, Auto-Scaling etc.). Anfangs könnte ein einzelner Server aber durchaus mehrere Bots bedienen, da die meisten zeitgleich ja nur relativ wenige Anfragen haben. Ein Technologiestack hier könnte sein: **Node.js mit NestJS oder Express** für das API, **PostgreSQL** für Daten (mit pgvector falls gebraucht), und ggf. Docker zur Containerisierung. Falls wir die Modelle lokal hosten (z. B. LLaMa 2 13B), bräuchten wir einen GPU-Server – evtl. mieten wir den nur zeitweise oder für bestimmte Kunden mit Extra-Anforderung.

  * Ein Kompromiss ist auch **Containers + Cloud Run/Fargate**: Also unsere eigene App in Container packen und auf Cloud Run (Google) oder AWS Fargate laufen lassen, was wiederum serverless nach Außen wirkt, aber mehr Freiheiten im Prozess bietet (z. B. längere Laufzeit, eigene ML-Bibliotheken). Das lässt sich ebenfalls gut skalieren und im Leerlauf auf 0 runterfahren, was Kosten spart.

**Kostenbewusstsein:** Unser Leitgedanke ist, möglichst **günstig zu starten**, aber **mitwachsend** zu designen. Daher könnten wir initial einen Firebase/Vercel-Mix nehmen (schnell, wenig Fixkosten) und bei steigender Nutzerzahl schrittweise zu dedizierten Ressourcen übergehen. Dienste wie Firebase, Vercel, Supabase haben alle kostenlose Kontingente, die wir ausnutzen können. Sobald wir über diese hinauswachsen, bedeutet es ja, dass wir Kunden und Umsatz haben, um die dann anfallenden Kosten zu decken. Wichtig ist, die **Effizienz pro Anfrage** im Auge zu behalten: LLM-Aufrufe dominieren vermutlich die variablen Kosten. Daher wie oben beschrieben Caching, Bottlenecks optimieren etc., damit die Nutzererfahrung schnell und unsere Rechnung schlank bleibt.

**Weitere Überlegungen:**

* **Monitoring & Fehlerhandling:** Wir sollten in der Infra Logs sammeln (z. B. mit Cloud Logging oder Sentry) um Fehler schnell zu finden. Auch ein Alarm, falls ein Bot plötzlich sehr viele Anfragen produziert (evtl. Missbrauch oder fehlerhafte Schleife), damit wir reagieren können.
* **Security:** Das Widget-Skript und API müssen sicher sein – API-Schlüssel oder Bot-IDs sollten nicht leicht auslesbar sein, Kommunikation natürlich über HTTPS. Evtl. Limitierung pro IP, um Missbrauch zu verhindern. Da wir Kundendaten verarbeiten, müssen wir uns auch um DSGVO kümmern (z. B. Auftragsverarbeitungsverträge, falls relevant).
* **Updates:** Da alle Kunden die gleiche Plattform teilen, profitieren alle von Verbesserungen sofort (klassischer SaaS-Vorteil). Wir müssen aber auf Abwärtskompatibilität achten, falls wir etwas ändern, was bestehende Bot-Konfigurationen beeinflusst.

## Zukünftige Erweiterungen (Kanäle und Features)

Sobald die **Website-Widget-Lösung** erfolgreich läuft, können wir das Angebot erweitern. Viele Klienten werden vielleicht nach **Integration in andere Kanäle** fragen, z. B. **WhatsApp, Facebook Messenger, Telegram oder auch die Firmen-Facebook-Seite**. Unsere Architektur können wir so auslegen, dass die Kernlogik des Bots kanalunabhängig funktioniert – der Unterschied liegt hauptsächlich in der *Schnittstelle zum Nutzer*. Das heißt, wir könnten für WhatsApp einen Connector bauen, der WhatsApp-Nachrichten (über die WhatsApp Business API) entgegennimmt, an unser zentrales Backend schickt und die Antwort zurück an WhatsApp postet. Ähnlich für Messenger (Facebook Graph API) oder Telegram (Bot API). Da unser Backend bereits REST/HTTP-APIs bietet, ist das relativ leicht adaptierbar. Viele moderne Botplattformen bieten Multi-Channel out-of-the-box – z. B. GPTBots erlaubt Integration des Chatbots in **WhatsApp, Discord, Telegram und mehr**. Dies können wir als **Upgrade-Option** anbieten, ggf. in höheren Preispaketen.

Des Weiteren könnten wir ein **Live-Chat-Handoff** integrieren: D.h. wenn der Bot an seine Grenzen kommt oder der Kunde explizit einen Menschen will, leiten wir den Chat an ein menschliches Chat-Tool weiter (z. B. über eine E-Mail-Benachrichtigung an einen Support-Mitarbeiter oder Integration in bestehende Live-Chat-Systeme, falls vorhanden). So haben Kunden das Beste aus beiden Welten – der Bot filtert und hilft, aber es gibt einen Fallback auf Human Support.

Ein weiteres Feature für die Zukunft ist evtl. ein **Trainingsmodus**: der Bot lernt aus vergangenen Interaktionen. Wir könnten dem Kunden vorschlagen, neue Q\&A-Paare anzulegen, wo der Bot häufig gepasst hat. Oder gar einen halb-automatischen **Feintuning-Service**, falls ein Kunde das Maximum rausholen will (das wäre dann allerdings kostenintensiver und komplexer).

Auch die **Spracherkennung (Voice)** wäre denkbar – etwa Integration in Telefonie oder Sprachassistenten – aber das ist ein ganz anderes Level und wahrscheinlich vorerst außerhalb unseres Fokus.

Zuletzt lohnt sich ein Blick auf **Wettbewerber**: Es gibt bereits einige SaaS-Angebote für webbasierte KI-Chatbots. Unsere Nische könnte sein, dass wir es speziell als Zusatzleistung für Webton-Klienten anbieten (evtl. integriert in andere Webservices von uns) und dass wir einen sehr **kundenfreundlichen, einfachen Ansatz** wählen. Die meisten Features, die wir planen, entsprechen dem, was auch andere No-Code Chatbot-Builder bieten: **Eigenes Knowledge-Upload, visuelle Anpassbarkeit, Multi-Channel, Analytics**. Mit einem cleveren Kostenmodell (z. B. Grundgebühr + nutzungsabhängig, oder verschiedene Pakete) stellen wir sicher, dass wir profitabel arbeiten. Durch die initial niedrigen Infrastrukturkosten (Firebase/Vercel) und die Möglichkeit, OpenAI-Kosten direkt weiterzugeben, halten wir das finanzielle Risiko gering, **bis das Produkt tragfähig skaliert**.

Zusammenfassend: Mit einem solchen AI-Chatbot-Service kann Webton seinen Klienten einen modernen Mehrwert bieten, der deren Websites aufwertet und Geschäftsziele unterstützt (Kundenservice, Leadgenerierung). Die Umsetzung erfordert eine clevere Kombination aus vorhandenen Cloud-Services und KI-APIs, um schnell, kosteneffizient und skalierbar zum Ziel zu kommen. Wenn wir die oben skizzierten Punkte beachten – Kernfunktionen, einfache Bedienung, Kostenkontrolle und Erweiterbarkeit – steht einem erfolgreichen Roll-out von **“AI Chatbots by Webton”** nichts im Wege!

**Quellen:** Die Konzeption wurde untermauert durch Best Practices und Beispiele aus ähnlichen Plattformen und Fachartikeln:

* Vorteile von Chatbot-Widgets und No-Code-Platform-Features
* Technische Ansätze für Retrieval-und KI-basierte Q\&A-Systeme
* Multi-Tenancy Prinzipien für SaaS-Chatbot-Lösungen
* Kostengegenüberstellung API-Nutzung vs. Eigenhosting von KI-Modellen
* Hinweise zur einfachen Integration auf Websites und in andere Kanäle.
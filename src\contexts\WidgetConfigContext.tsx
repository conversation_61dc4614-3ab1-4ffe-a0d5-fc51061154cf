import React, { createContext, useContext, useState, ReactNode } from "react";

export interface WidgetConfig {
  companyName: string;
  botName: string;
  logo: string;
  primaryColor: string;
  secondaryColor: string;
  greetingMessage: string;
  position: "bottom-right" | "bottom-left";
  widgetId: string;
}

interface WidgetConfigContextType {
  config: WidgetConfig;
  updateConfig: (updates: Partial<WidgetConfig>) => void;
  generateEmbedCode: () => string;
}

const defaultConfig: WidgetConfig = {
  companyName: "Your Company",
  botName: "AI Assistant",
  logo: "https://api.dicebear.com/7.x/avataaars/svg?seed=chatbot",
  primaryColor: "#4f46e5",
  secondaryColor: "#ffffff",
  greetingMessage: "Hello! How can I help you today?",
  position: "bottom-right",
  widgetId: Math.random().toString(36).substring(2, 10),
};

const WidgetConfigContext = createContext<WidgetConfigContextType | undefined>(
  undefined,
);

export const useWidgetConfig = () => {
  const context = useContext(WidgetConfigContext);
  if (!context) {
    throw new Error(
      "useWidgetConfig must be used within a WidgetConfigProvider",
    );
  }
  return context;
};

interface WidgetConfigProviderProps {
  children: ReactNode;
}

export const WidgetConfigProvider: React.FC<WidgetConfigProviderProps> = ({
  children,
}) => {
  const [config, setConfig] = useState<WidgetConfig>(defaultConfig);

  const updateConfig = (updates: Partial<WidgetConfig>) => {
    setConfig((prev) => ({ ...prev, ...updates }));
  };

  const generateEmbedCode = () => {
    const baseUrl = window.location.origin;
    return `<script src="${baseUrl}/chatbot-widget.js" 
  data-widget-id="${config.widgetId}"
  data-company-name="${config.companyName}"
  data-bot-name="${config.botName}"
  data-logo="${config.logo}"
  data-primary-color="${config.primaryColor}"
  data-secondary-color="${config.secondaryColor}"
  data-greeting="${config.greetingMessage}"
  data-position="${config.position}"
  async>
</script>`;
  };

  return (
    <WidgetConfigContext.Provider
      value={{
        config,
        updateConfig,
        generateEmbedCode,
      }}
    >
      {children}
    </WidgetConfigContext.Provider>
  );
};

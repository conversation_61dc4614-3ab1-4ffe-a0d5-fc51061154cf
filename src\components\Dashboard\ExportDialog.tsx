/**
 * Export Dialog Component
 * 
 * Provides export functionality for analytics data in various formats.
 * Supports CSV, JSON exports with customizable date ranges and metrics.
 */

import React, { useState } from 'react';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  Download, 
  FileText, 
  Database, 
  Calendar,
  CheckCircle,
  Loader2,
} from 'lucide-react';
import { format } from 'date-fns';
import { saveAs } from 'file-saver';

import { AnalyticsMetrics, TimeSeriesData } from '@/types/api';
import { AnalyticsExportUtils } from '@/services/analytics-mock';

interface ExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  botId: string;
  dateRange: { from: Date; to: Date };
  metrics: AnalyticsMetrics | null;
  timeSeriesData: TimeSeriesData[];
}

type ExportFormat = 'csv' | 'json';
type ExportType = 'summary' | 'timeseries' | 'both';

interface ExportOptions {
  format: ExportFormat;
  type: ExportType;
  includeMetrics: {
    conversations: boolean;
    messages: boolean;
    users: boolean;
    responseTime: boolean;
    satisfaction: boolean;
    resolutionRate: boolean;
  };
}

export function ExportDialog({
  open,
  onOpenChange,
  botId,
  dateRange,
  metrics,
  timeSeriesData,
}: ExportDialogProps) {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'csv',
    type: 'both',
    includeMetrics: {
      conversations: true,
      messages: true,
      users: true,
      responseTime: true,
      satisfaction: true,
      resolutionRate: true,
    },
  });
  const [isExporting, setIsExporting] = useState(false);
  const [exportComplete, setExportComplete] = useState(false);

  /**
   * Handle metric selection change
   */
  const handleMetricChange = (metric: keyof ExportOptions['includeMetrics'], checked: boolean) => {
    setExportOptions(prev => ({
      ...prev,
      includeMetrics: {
        ...prev.includeMetrics,
        [metric]: checked,
      },
    }));
  };

  /**
   * Prepare summary data for export
   */
  const prepareSummaryData = () => {
    if (!metrics) return [];

    const data: any = {
      bot_id: botId,
      export_date: new Date().toISOString(),
      period_start: dateRange.from.toISOString(),
      period_end: dateRange.to.toISOString(),
      period_days: Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24)),
    };

    if (exportOptions.includeMetrics.conversations) {
      data.total_conversations = metrics.total_conversations;
    }
    if (exportOptions.includeMetrics.messages) {
      data.total_messages = metrics.total_messages;
    }
    if (exportOptions.includeMetrics.users) {
      data.unique_users = metrics.unique_users;
    }
    if (exportOptions.includeMetrics.responseTime) {
      data.average_response_time = metrics.average_response_time;
    }
    if (exportOptions.includeMetrics.satisfaction) {
      data.satisfaction_score = metrics.satisfaction_score;
    }
    if (exportOptions.includeMetrics.resolutionRate) {
      data.resolution_rate = metrics.resolution_rate;
    }

    return [data];
  };

  /**
   * Prepare time series data for export
   */
  const prepareTimeSeriesData = () => {
    return timeSeriesData.map(item => {
      const data: any = {
        timestamp: item.timestamp,
        date: format(new Date(item.timestamp), 'yyyy-MM-dd'),
        time: format(new Date(item.timestamp), 'HH:mm:ss'),
      };

      if (exportOptions.includeMetrics.conversations) {
        data.conversations = item.conversations;
      }
      if (exportOptions.includeMetrics.messages) {
        data.messages = item.messages;
      }
      if (exportOptions.includeMetrics.responseTime) {
        data.response_time = item.response_time;
      }
      if (exportOptions.includeMetrics.satisfaction) {
        data.satisfaction_score = item.satisfaction_score;
      }

      return data;
    });
  };

  /**
   * Generate filename for export
   */
  const generateFilename = (type: string) => {
    const dateStr = format(new Date(), 'yyyy-MM-dd');
    const periodStr = `${format(dateRange.from, 'MMM-dd')}-to-${format(dateRange.to, 'MMM-dd')}`;
    return `webton-analytics-${type}-${periodStr}-${dateStr}`;
  };

  /**
   * Export data in CSV format
   */
  const exportCSV = async () => {
    try {
      if (exportOptions.type === 'summary' || exportOptions.type === 'both') {
        const summaryData = prepareSummaryData();
        const csvContent = AnalyticsExportUtils.toCSV(summaryData);
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
        saveAs(blob, `${generateFilename('summary')}.csv`);
      }

      if (exportOptions.type === 'timeseries' || exportOptions.type === 'both') {
        const timeSeriesExportData = prepareTimeSeriesData();
        const csvContent = AnalyticsExportUtils.toCSV(timeSeriesExportData);
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
        saveAs(blob, `${generateFilename('timeseries')}.csv`);
      }
    } catch (error) {
      console.error('CSV export failed:', error);
      throw error;
    }
  };

  /**
   * Export data in JSON format
   */
  const exportJSON = async () => {
    try {
      const exportData: any = {
        metadata: {
          bot_id: botId,
          export_date: new Date().toISOString(),
          period: {
            start: dateRange.from.toISOString(),
            end: dateRange.to.toISOString(),
            days: Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24)),
          },
          export_options: exportOptions,
        },
      };

      if (exportOptions.type === 'summary' || exportOptions.type === 'both') {
        exportData.summary = prepareSummaryData()[0];
      }

      if (exportOptions.type === 'timeseries' || exportOptions.type === 'both') {
        exportData.time_series = prepareTimeSeriesData();
      }

      const jsonContent = JSON.stringify(exportData, null, 2);
      const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8' });
      saveAs(blob, `${generateFilename('export')}.json`);
    } catch (error) {
      console.error('JSON export failed:', error);
      throw error;
    }
  };

  /**
   * Handle export action
   */
  const handleExport = async () => {
    try {
      setIsExporting(true);
      setExportComplete(false);

      // Simulate export delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (exportOptions.format === 'csv') {
        await exportCSV();
      } else {
        await exportJSON();
      }

      setExportComplete(true);
      
      // Auto-close dialog after successful export
      setTimeout(() => {
        onOpenChange(false);
        setExportComplete(false);
      }, 2000);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  /**
   * Check if export is valid
   */
  const isExportValid = () => {
    const hasSelectedMetrics = Object.values(exportOptions.includeMetrics).some(Boolean);
    const hasData = (exportOptions.type === 'summary' && metrics) || 
                   (exportOptions.type === 'timeseries' && timeSeriesData.length > 0) ||
                   (exportOptions.type === 'both' && metrics && timeSeriesData.length > 0);
    return hasSelectedMetrics && hasData;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Analytics Data
          </DialogTitle>
          <DialogDescription>
            Export your analytics data for external analysis or reporting.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Export period info */}
          <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">
              {format(dateRange.from, 'MMM dd, yyyy')} - {format(dateRange.to, 'MMM dd, yyyy')}
            </span>
            <Badge variant="outline" className="text-xs">
              {Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24))} days
            </Badge>
          </div>

          {/* Export format */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Export Format</Label>
            <div className="flex gap-2">
              <Button
                variant={exportOptions.format === 'csv' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setExportOptions(prev => ({ ...prev, format: 'csv' }))}
                className="flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                CSV
              </Button>
              <Button
                variant={exportOptions.format === 'json' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setExportOptions(prev => ({ ...prev, format: 'json' }))}
                className="flex items-center gap-2"
              >
                <Database className="h-4 w-4" />
                JSON
              </Button>
            </div>
          </div>

          {/* Export type */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Data Type</Label>
            <div className="flex gap-2">
              {[
                { value: 'summary', label: 'Summary Only' },
                { value: 'timeseries', label: 'Time Series Only' },
                { value: 'both', label: 'Both' },
              ].map(({ value, label }) => (
                <Button
                  key={value}
                  variant={exportOptions.type === value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setExportOptions(prev => ({ ...prev, type: value as ExportType }))}
                >
                  {label}
                </Button>
              ))}
            </div>
          </div>

          <Separator />

          {/* Metrics selection */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Include Metrics</Label>
            <div className="grid grid-cols-2 gap-3">
              {[
                { key: 'conversations', label: 'Conversations' },
                { key: 'messages', label: 'Messages' },
                { key: 'users', label: 'Unique Users' },
                { key: 'responseTime', label: 'Response Time' },
                { key: 'satisfaction', label: 'Satisfaction' },
                { key: 'resolutionRate', label: 'Resolution Rate' },
              ].map(({ key, label }) => (
                <div key={key} className="flex items-center space-x-2">
                  <Checkbox
                    id={key}
                    checked={exportOptions.includeMetrics[key as keyof ExportOptions['includeMetrics']]}
                    onCheckedChange={(checked) => 
                      handleMetricChange(key as keyof ExportOptions['includeMetrics'], checked as boolean)
                    }
                  />
                  <Label htmlFor={key} className="text-sm">
                    {label}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleExport} 
            disabled={!isExportValid() || isExporting}
            className="min-w-[100px]"
          >
            {isExporting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Exporting...
              </>
            ) : exportComplete ? (
              <>
                <CheckCircle className="mr-2 h-4 w-4" />
                Complete!
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Export
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

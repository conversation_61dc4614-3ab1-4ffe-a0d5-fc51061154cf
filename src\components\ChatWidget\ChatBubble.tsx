import React, { useState } from "react";
import { motion } from "framer-motion";
import { MessageCircle, X } from "lucide-react";

interface ChatBubbleProps {
  isOpen?: boolean;
  onToggle?: () => void;
  onClick?: () => void;
  logo?: string;
  primaryColor?: string;
  secondaryColor?: string;
}

const ChatBubble = ({
  isOpen = false,
  onToggle = () => {},
  onClick,
  logo = "https://api.dicebear.com/7.x/avataaars/svg?seed=chatbot",
  primaryColor = "#4f46e5",
  secondaryColor = "#ffffff",
}: ChatBubbleProps) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      className="fixed bottom-6 right-6 z-50"
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <motion.button
        className="flex items-center justify-center rounded-full shadow-lg focus:outline-none"
        style={{
          backgroundColor: primaryColor,
          width: "60px",
          height: "60px",
        }}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={onClick || onToggle}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        aria-label={isOpen ? "Close chat" : "Open chat"}
      >
        {isOpen ? (
          <X size={24} color={secondaryColor} />
        ) : (
          <div className="flex items-center justify-center w-full h-full">
            {logo ? (
              <div className="w-8 h-8 rounded-full overflow-hidden">
                <img
                  src={logo}
                  alt="Chat logo"
                  className="w-full h-full object-cover"
                />
              </div>
            ) : (
              <MessageCircle size={24} color={secondaryColor} />
            )}
          </div>
        )}
      </motion.button>

      {isHovered && !isOpen && (
        <motion.div
          className="absolute bottom-16 right-0 bg-white px-4 py-2 rounded-lg shadow-md"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2 }}
        >
          <p className="text-sm whitespace-nowrap">Chat with us</p>
        </motion.div>
      )}
    </motion.div>
  );
};

export default ChatBubble;

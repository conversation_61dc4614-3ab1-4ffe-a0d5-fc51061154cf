/**
 * File Upload Component with <PERSON>ag and Drop
 * 
 * Provides a comprehensive file upload interface for knowledge base documents
 * with drag-and-drop functionality, file validation, and progress tracking.
 */

import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Upload,
  File,
  X,
  AlertCircle,
  CheckCircle,
  FileText,
  FileImage,
  FileSpreadsheet,
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface FileUploadData {
  file: File;
  title: string;
  description: string;
}

interface FileUploadProps {
  onUpload: (data: FileUploadData) => Promise<void>;
  onCancel?: () => void;
  maxFileSize?: number; // in MB
  acceptedFileTypes?: string[];
  className?: string;
}

interface UploadFile extends File {
  id: string;
  title: string;
  description: string;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
}

const ACCEPTED_FILE_TYPES = {
  'application/pdf': ['.pdf'],
  'text/plain': ['.txt'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
  'text/markdown': ['.md'],
  'text/csv': ['.csv'],
  'application/vnd.ms-excel': ['.xls'],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
};

const DEFAULT_MAX_FILE_SIZE = 10; // 10MB

export const FileUpload: React.FC<FileUploadProps> = ({
  onUpload,
  onCancel,
  maxFileSize = DEFAULT_MAX_FILE_SIZE,
  acceptedFileTypes = Object.keys(ACCEPTED_FILE_TYPES),
  className,
}) => {
  const [files, setFiles] = useState<UploadFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  const getFileIcon = (file: File) => {
    if (file.type.includes('pdf')) return <FileText className="h-6 w-6 text-red-500" />;
    if (file.type.includes('image')) return <FileImage className="h-6 w-6 text-blue-500" />;
    if (file.type.includes('spreadsheet') || file.type.includes('excel') || file.type.includes('csv')) {
      return <FileSpreadsheet className="h-6 w-6 text-green-500" />;
    }
    return <File className="h-6 w-6 text-gray-500" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const validateFile = (file: File): string | null => {
    // Check file size
    if (file.size > maxFileSize * 1024 * 1024) {
      return `File size must be less than ${maxFileSize}MB`;
    }

    // Check file type
    if (!acceptedFileTypes.includes(file.type)) {
      return 'File type not supported';
    }

    return null;
  };

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    // Handle rejected files
    if (rejectedFiles.length > 0) {
      const errors = rejectedFiles.map(({ file, errors }) => 
        `${file.name}: ${errors.map((e: any) => e.message).join(', ')}`
      );
      console.error('Rejected files:', errors);
    }

    // Process accepted files
    const newFiles: UploadFile[] = acceptedFiles.map(file => {
      const error = validateFile(file);
      return {
        ...file,
        id: Math.random().toString(36).substr(2, 9),
        title: file.name.replace(/\.[^/.]+$/, ''), // Remove extension
        description: '',
        progress: 0,
        status: error ? 'error' : 'pending',
        error,
      } as UploadFile;
    });

    setFiles(prev => [...prev, ...newFiles]);
  }, [maxFileSize, acceptedFileTypes]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedFileTypes.reduce((acc, type) => {
      acc[type] = ACCEPTED_FILE_TYPES[type as keyof typeof ACCEPTED_FILE_TYPES] || [];
      return acc;
    }, {} as Record<string, string[]>),
    maxSize: maxFileSize * 1024 * 1024,
    multiple: true,
  });

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const updateFileMetadata = (fileId: string, field: 'title' | 'description', value: string) => {
    setFiles(prev => prev.map(f => 
      f.id === fileId ? { ...f, [field]: value } : f
    ));
  };

  const uploadFiles = async () => {
    const validFiles = files.filter(f => f.status === 'pending');
    if (validFiles.length === 0) return;

    setIsUploading(true);

    for (const file of validFiles) {
      try {
        // Update status to uploading
        setFiles(prev => prev.map(f => 
          f.id === file.id ? { ...f, status: 'uploading', progress: 0 } : f
        ));

        // Simulate progress updates
        const progressInterval = setInterval(() => {
          setFiles(prev => prev.map(f => {
            if (f.id === file.id && f.progress < 90) {
              return { ...f, progress: f.progress + 10 };
            }
            return f;
          }));
        }, 200);

        // Call the upload function
        await onUpload({
          file,
          title: file.title || file.name,
          description: file.description,
        });

        clearInterval(progressInterval);

        // Update status to success
        setFiles(prev => prev.map(f => 
          f.id === file.id ? { ...f, status: 'success', progress: 100 } : f
        ));

      } catch (error) {
        // Update status to error
        setFiles(prev => prev.map(f => 
          f.id === file.id ? { 
            ...f, 
            status: 'error', 
            error: error instanceof Error ? error.message : 'Upload failed' 
          } : f
        ));
      }
    }

    setIsUploading(false);
  };

  const hasValidFiles = files.some(f => f.status === 'pending');
  const hasErrors = files.some(f => f.status === 'error');

  return (
    <div className={cn('space-y-6', className)}>
      {/* Drag and Drop Area */}
      <Card>
        <CardContent className="p-6">
          <div
            {...getRootProps()}
            className={cn(
              'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors',
              isDragActive 
                ? 'border-primary bg-primary/5' 
                : 'border-muted-foreground/25 hover:border-primary/50'
            )}
          >
            <input {...getInputProps()} />
            <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">
              {isDragActive ? 'Drop files here' : 'Upload Documents'}
            </h3>
            <p className="text-muted-foreground mb-4">
              Drag and drop files here, or click to browse
            </p>
            <div className="flex flex-wrap justify-center gap-2 mb-4">
              {Object.values(ACCEPTED_FILE_TYPES).flat().map(ext => (
                <Badge key={ext} variant="secondary" className="text-xs">
                  {ext}
                </Badge>
              ))}
            </div>
            <p className="text-sm text-muted-foreground">
              Maximum file size: {maxFileSize}MB
            </p>
          </div>
        </CardContent>
      </Card>

      {/* File List */}
      {files.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Files to Upload</h3>
          {files.map(file => (
            <Card key={file.id}>
              <CardContent className="p-4">
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0">
                    {getFileIcon(file)}
                  </div>
                  
                  <div className="flex-1 space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{file.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {formatFileSize(file.size)}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        {file.status === 'success' && (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        )}
                        {file.status === 'error' && (
                          <AlertCircle className="h-5 w-5 text-red-500" />
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(file.id)}
                          disabled={file.status === 'uploading'}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {file.status === 'pending' && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                          <Label htmlFor={`title-${file.id}`}>Title</Label>
                          <Input
                            id={`title-${file.id}`}
                            value={file.title}
                            onChange={(e) => updateFileMetadata(file.id, 'title', e.target.value)}
                            placeholder="Document title"
                          />
                        </div>
                        <div>
                          <Label htmlFor={`description-${file.id}`}>Description</Label>
                          <Textarea
                            id={`description-${file.id}`}
                            value={file.description}
                            onChange={(e) => updateFileMetadata(file.id, 'description', e.target.value)}
                            placeholder="Brief description (optional)"
                            rows={1}
                          />
                        </div>
                      </div>
                    )}

                    {file.status === 'uploading' && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Uploading...</span>
                          <span>{file.progress}%</span>
                        </div>
                        <Progress value={file.progress} />
                      </div>
                    )}

                    {file.status === 'error' && file.error && (
                      <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>{file.error}</AlertDescription>
                      </Alert>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Action Buttons */}
      {files.length > 0 && (
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isUploading}
          >
            Cancel
          </Button>
          <Button
            onClick={uploadFiles}
            disabled={!hasValidFiles || isUploading}
          >
            {isUploading ? 'Uploading...' : `Upload ${files.filter(f => f.status === 'pending').length} Files`}
          </Button>
        </div>
      )}

      {hasErrors && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Some files have errors. Please fix them before uploading.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

/**
 * Widget Entry Point
 * 
 * Main entry file for the standalone embeddable chat widget.
 * This file creates the global WebtonChatbot object and handles widget initialization.
 */

import React from 'react';
import { createRoot, Root } from 'react-dom/client';
import { StandaloneWidget } from './StandaloneWidget';
import { WidgetConfig, WidgetAPI } from './types';
import { validateConfig, sanitizeConfig } from './utils/config';
import { createSecureContainer } from './utils/security';
import './widget.css';

/**
 * Global widget configuration and state
 */
interface WidgetState {
  isInitialized: boolean;
  isOpen: boolean;
  config: WidgetConfig | null;
  container: HTMLElement | null;
  root: Root | null;
  sessionId: string;
}

/**
 * Widget instance state
 */
const widgetState: WidgetState = {
  isInitialized: false,
  isOpen: false,
  config: null,
  container: null,
  root: null,
  sessionId: generateSessionId(),
};

/**
 * Event listeners for widget events
 */
const eventListeners: Map<string, Set<Function>> = new Map();

/**
 * Generate a unique session ID for the widget
 */
function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
}

/**
 * Parse configuration from script tag attributes
 */
function parseConfigFromScript(): WidgetConfig | null {
  const scripts = document.querySelectorAll('script[src*="chatbot-widget"]');
  const script = scripts[scripts.length - 1] as HTMLScriptElement;
  
  if (!script) {
    console.error('[WebtonChatbot] Widget script not found');
    return null;
  }

  const config: Partial<WidgetConfig> = {
    botId: script.dataset.botId || script.dataset.widgetId,
    companyName: script.dataset.companyName,
    botName: script.dataset.botName,
    primaryColor: script.dataset.primaryColor,
    secondaryColor: script.dataset.secondaryColor,
    position: (script.dataset.position as 'bottom-right' | 'bottom-left') || 'bottom-right',
    greetingMessage: script.dataset.greeting || script.dataset.greetingMessage,
    logo: script.dataset.logo,
    apiEndpoint: script.dataset.apiEndpoint,
    websocketEndpoint: script.dataset.websocketEndpoint,
    theme: (script.dataset.theme as 'light' | 'dark' | 'auto') || 'light',
    debug: script.dataset.debug === 'true',
  };

  return validateConfig(config);
}

/**
 * Initialize the widget
 */
function initializeWidget(): void {
  if (widgetState.isInitialized) {
    console.warn('[WebtonChatbot] Widget already initialized');
    return;
  }

  try {
    // Parse configuration from script tag
    const config = parseConfigFromScript();
    if (!config) {
      throw new Error('Invalid widget configuration');
    }

    // Sanitize configuration for security
    widgetState.config = sanitizeConfig(config);

    // Create secure container for the widget
    widgetState.container = createSecureContainer(config.position);
    document.body.appendChild(widgetState.container);

    // Create React root and render widget
    widgetState.root = createRoot(widgetState.container);
    renderWidget();

    widgetState.isInitialized = true;

    // Emit initialization event
    emitEvent('initialized', { config: widgetState.config });

    if (config.debug) {
      console.log('[WebtonChatbot] Widget initialized successfully', widgetState.config);
    }
  } catch (error) {
    console.error('[WebtonChatbot] Failed to initialize widget:', error);
    emitEvent('error', { error: error instanceof Error ? error.message : 'Unknown error' });
  }
}

/**
 * Render the widget component
 */
function renderWidget(): void {
  if (!widgetState.root || !widgetState.config) {
    return;
  }

  widgetState.root.render(
    React.createElement(StandaloneWidget, {
      config: widgetState.config,
      isOpen: widgetState.isOpen,
      sessionId: widgetState.sessionId,
      onToggle: handleToggle,
      onMessage: handleMessage,
      onError: handleError,
    })
  );
}

/**
 * Handle widget toggle
 */
function handleToggle(): void {
  widgetState.isOpen = !widgetState.isOpen;
  renderWidget();
  
  emitEvent(widgetState.isOpen ? 'opened' : 'closed', {
    sessionId: widgetState.sessionId,
  });
}

/**
 * Handle message events
 */
function handleMessage(message: string, type: 'user' | 'bot'): void {
  emitEvent('message', {
    message,
    type,
    sessionId: widgetState.sessionId,
    timestamp: new Date().toISOString(),
  });
}

/**
 * Handle error events
 */
function handleError(error: string): void {
  console.error('[WebtonChatbot] Widget error:', error);
  emitEvent('error', { error, sessionId: widgetState.sessionId });
}

/**
 * Emit custom events
 */
function emitEvent(eventType: string, data: any): void {
  const listeners = eventListeners.get(eventType);
  if (listeners) {
    listeners.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error('[WebtonChatbot] Error in event listener:', error);
      }
    });
  }

  // Also dispatch as DOM event
  const customEvent = new CustomEvent(`webton:${eventType}`, {
    detail: data,
    bubbles: false,
    cancelable: false,
  });
  document.dispatchEvent(customEvent);
}

/**
 * Public API for the widget
 */
const WebtonChatbotAPI: WidgetAPI = {
  // Widget control methods
  open(): void {
    if (!widgetState.isInitialized) {
      console.warn('[WebtonChatbot] Widget not initialized');
      return;
    }
    if (!widgetState.isOpen) {
      handleToggle();
    }
  },

  close(): void {
    if (!widgetState.isInitialized) {
      console.warn('[WebtonChatbot] Widget not initialized');
      return;
    }
    if (widgetState.isOpen) {
      handleToggle();
    }
  },

  toggle(): void {
    if (!widgetState.isInitialized) {
      console.warn('[WebtonChatbot] Widget not initialized');
      return;
    }
    handleToggle();
  },

  // Message methods
  sendMessage(message: string): void {
    if (!widgetState.isInitialized) {
      console.warn('[WebtonChatbot] Widget not initialized');
      return;
    }
    
    // Open widget if closed
    if (!widgetState.isOpen) {
      this.open();
    }

    // Emit message event for the widget to handle
    emitEvent('sendMessage', { message });
  },

  // Event handling
  on(eventType: string, callback: Function): void {
    if (!eventListeners.has(eventType)) {
      eventListeners.set(eventType, new Set());
    }
    eventListeners.get(eventType)!.add(callback);
  },

  off(eventType: string, callback: Function): void {
    const listeners = eventListeners.get(eventType);
    if (listeners) {
      listeners.delete(callback);
    }
  },

  // Configuration methods
  updateConfig(updates: Partial<WidgetConfig>): void {
    if (!widgetState.isInitialized || !widgetState.config) {
      console.warn('[WebtonChatbot] Widget not initialized');
      return;
    }

    const newConfig = { ...widgetState.config, ...updates };
    const validatedConfig = validateConfig(newConfig);
    
    if (validatedConfig) {
      widgetState.config = sanitizeConfig(validatedConfig);
      renderWidget();
      emitEvent('configUpdated', { config: widgetState.config });
    }
  },

  getConfig(): WidgetConfig | null {
    return widgetState.config;
  },

  // State methods
  isOpen(): boolean {
    return widgetState.isOpen;
  },

  isInitialized(): boolean {
    return widgetState.isInitialized;
  },

  getSessionId(): string {
    return widgetState.sessionId;
  },

  // Utility methods
  destroy(): void {
    if (widgetState.root) {
      widgetState.root.unmount();
    }
    if (widgetState.container && widgetState.container.parentNode) {
      widgetState.container.parentNode.removeChild(widgetState.container);
    }
    
    // Clear state
    widgetState.isInitialized = false;
    widgetState.isOpen = false;
    widgetState.config = null;
    widgetState.container = null;
    widgetState.root = null;
    
    // Clear event listeners
    eventListeners.clear();
    
    emitEvent('destroyed', {});
  },
};

/**
 * Auto-initialize when DOM is ready
 */
function autoInitialize(): void {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeWidget);
  } else {
    // DOM is already ready
    setTimeout(initializeWidget, 0);
  }
}

// Expose global API
declare global {
  interface Window {
    WebtonChatbot: WidgetAPI;
  }
}

window.WebtonChatbot = WebtonChatbotAPI;

// Auto-initialize the widget
autoInitialize();

// Export for module systems
export default WebtonChatbotAPI;

/**
 * AuthContext Unit Tests
 * 
 * Tests for authentication context functionality, state management,
 * and permission checking.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AuthProvider, useAuth } from '../../../src/contexts/AuthContext';

// Mock Supabase
const mockSupabase = {
  auth: {
    signInWithPassword: vi.fn(),
    signUp: vi.fn(),
    signOut: vi.fn(),
    resetPasswordForEmail: vi.fn(),
    onAuthStateChange: vi.fn(),
    getSession: vi.fn(),
    getUser: vi.fn(),
  },
  from: vi.fn(() => ({
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    single: vi.fn(),
  })),
};

vi.mock('../../../src/lib/supabase', () => ({
  supabase: mockSupabase,
  ROLES: {
    ADMIN: 'admin',
    CLIENT: 'client',
    VIEWER: 'viewer',
  },
  permissions: {
    canManageUsers: vi.fn((profile) => profile?.role === 'admin'),
    canManageBots: vi.fn((profile) => ['admin', 'client'].includes(profile?.role)),
    canViewAnalytics: vi.fn((profile) => ['admin', 'client', 'viewer'].includes(profile?.role)),
    canEditSettings: vi.fn((profile) => ['admin', 'client'].includes(profile?.role)),
    canExportData: vi.fn((profile) => ['admin', 'client'].includes(profile?.role)),
  },
}));

// Test component to access auth context
const TestComponent = () => {
  const auth = useAuth();
  
  return (
    <div>
      <div data-testid="loading">{auth.loading ? 'loading' : 'loaded'}</div>
      <div data-testid="user">{auth.user?.email || 'no-user'}</div>
      <div data-testid="role">{auth.profile?.role || 'no-role'}</div>
      <div data-testid="can-manage-users">{auth.canManageUsers ? 'yes' : 'no'}</div>
      <div data-testid="can-manage-bots">{auth.canManageBots ? 'yes' : 'no'}</div>
      <button onClick={() => auth.signIn('<EMAIL>', 'password')}>
        Sign In
      </button>
      <button onClick={() => auth.signOut()}>Sign Out</button>
    </div>
  );
};

describe('AuthContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mock implementation for auth state change
    mockSupabase.auth.onAuthStateChange.mockImplementation((callback) => {
      // Simulate initial auth state
      setTimeout(() => callback('INITIAL_SESSION', null), 0);
      return {
        data: {
          subscription: {
            unsubscribe: vi.fn(),
          },
        },
      };
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should provide initial loading state', () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    expect(screen.getByTestId('loading')).toHaveTextContent('loading');
    expect(screen.getByTestId('user')).toHaveTextContent('no-user');
  });

  it('should handle successful sign in', async () => {
    const mockUser = {
      id: 'user-123',
      email: '<EMAIL>',
      aud: 'authenticated',
      role: 'authenticated',
    };

    const mockProfile = {
      id: 'user-123',
      email: '<EMAIL>',
      full_name: 'Test User',
      role: 'client',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
    };

    mockSupabase.auth.signInWithPassword.mockResolvedValue({
      data: { user: mockUser, session: { user: mockUser } },
      error: null,
    });

    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({
        data: mockProfile,
        error: null,
      }),
    });

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    const signInButton = screen.getByText('Sign In');
    await userEvent.click(signInButton);

    await waitFor(() => {
      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password',
      });
    });
  });

  it('should handle sign out', async () => {
    mockSupabase.auth.signOut.mockResolvedValue({ error: null });

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    const signOutButton = screen.getByText('Sign Out');
    await userEvent.click(signOutButton);

    await waitFor(() => {
      expect(mockSupabase.auth.signOut).toHaveBeenCalled();
    });
  });

  it('should calculate permissions correctly for admin role', async () => {
    const mockProfile = {
      id: 'admin-123',
      email: '<EMAIL>',
      role: 'admin',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
    };

    // Mock the auth state change to simulate logged in admin
    mockSupabase.auth.onAuthStateChange.mockImplementation((callback) => {
      setTimeout(() => {
        callback('SIGNED_IN', {
          user: { id: 'admin-123', email: '<EMAIL>' },
        });
      }, 0);
      return {
        data: {
          subscription: { unsubscribe: vi.fn() },
        },
      };
    });

    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({
        data: mockProfile,
        error: null,
      }),
    });

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('can-manage-users')).toHaveTextContent('yes');
      expect(screen.getByTestId('can-manage-bots')).toHaveTextContent('yes');
    });
  });

  it('should calculate permissions correctly for viewer role', async () => {
    const mockProfile = {
      id: 'viewer-123',
      email: '<EMAIL>',
      role: 'viewer',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
    };

    mockSupabase.auth.onAuthStateChange.mockImplementation((callback) => {
      setTimeout(() => {
        callback('SIGNED_IN', {
          user: { id: 'viewer-123', email: '<EMAIL>' },
        });
      }, 0);
      return {
        data: {
          subscription: { unsubscribe: vi.fn() },
        },
      };
    });

    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({
        data: mockProfile,
        error: null,
      }),
    });

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('can-manage-users')).toHaveTextContent('no');
      expect(screen.getByTestId('can-manage-bots')).toHaveTextContent('no');
    });
  });

  it('should throw error when useAuth is used outside provider', () => {
    // Suppress console.error for this test
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    expect(() => {
      render(<TestComponent />);
    }).toThrow('useAuth must be used within an AuthProvider');

    consoleSpy.mockRestore();
  });
});

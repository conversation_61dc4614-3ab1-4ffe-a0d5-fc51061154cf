import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  MessageSquare,
  Settings,
  BarChart3,
  FileText,
  Users,
} from "lucide-react";
import WidgetPreview from "./WidgetPreview";

const Home = () => {
  return (
    <div className="min-h-screen bg-background p-6 md:p-10">
      <header className="mb-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Chatbot Widget Dashboard
            </h1>
            <p className="mt-1 text-muted-foreground">
              Manage and customize your AI-powered chatbot widget
            </p>
          </div>
          <div className="mt-4 md:mt-0 flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              Active Plan: Professional
            </Badge>
            <Button variant="outline" size="sm">
              <Settings className="mr-2 h-4 w-4" />
              Account Settings
            </Button>
          </div>
        </div>
      </header>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Tabs defaultValue="preview" className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="preview">
                <MessageSquare className="mr-2 h-4 w-4" />
                Widget Preview
              </TabsTrigger>
              <TabsTrigger value="analytics">
                <BarChart3 className="mr-2 h-4 w-4" />
                Analytics
              </TabsTrigger>
              <TabsTrigger value="knowledge">
                <FileText className="mr-2 h-4 w-4" />
                Knowledge Base
              </TabsTrigger>
              <TabsTrigger value="conversations">
                <Users className="mr-2 h-4 w-4" />
                Conversations
              </TabsTrigger>
            </TabsList>

            <TabsContent value="preview" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Widget Preview</CardTitle>
                  <CardDescription>
                    See how your chatbot will appear on your website. Click the
                    chat bubble to interact with it.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <WidgetPreview />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Analytics Dashboard</CardTitle>
                  <CardDescription>
                    Track usage metrics and performance of your chatbot.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-2xl font-bold">1,245</div>
                        <p className="text-xs text-muted-foreground">
                          Total Conversations
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-2xl font-bold">87%</div>
                        <p className="text-xs text-muted-foreground">
                          Resolution Rate
                        </p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="pt-6">
                        <div className="text-2xl font-bold">68</div>
                        <p className="text-xs text-muted-foreground">
                          Leads Generated
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                  <div className="h-[300px] mt-6 bg-muted rounded-md flex items-center justify-center">
                    <p className="text-muted-foreground">
                      Analytics charts will appear here
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="knowledge" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Knowledge Base</CardTitle>
                  <CardDescription>
                    Manage the information your chatbot can access and use to
                    answer questions.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="font-medium">Documents & FAQs</h3>
                      <Button size="sm">Upload Document</Button>
                    </div>
                    <div className="border rounded-md p-4">
                      <p className="text-center text-muted-foreground py-8">
                        No documents uploaded yet. Add documents or FAQs to
                        train your chatbot.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="conversations" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Conversations</CardTitle>
                  <CardDescription>
                    Review recent interactions with your chatbot.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="border rounded-md divide-y">
                    {[1, 2, 3].map((i) => (
                      <div
                        key={i}
                        className="p-4 hover:bg-muted/50 cursor-pointer"
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium">
                              Visitor #{i}0{i}5
                            </p>
                            <p className="text-sm text-muted-foreground mt-1">
                              "How do I reset my password?"
                            </p>
                          </div>
                          <Badge variant="outline">
                            {i} hour{i !== 1 ? "s" : ""} ago
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Widget Configuration</CardTitle>
              <CardDescription>
                Customize how your chatbot looks and behaves.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Widget Name</label>
                  <input
                    type="text"
                    className="w-full mt-1 px-3 py-2 border rounded-md"
                    defaultValue="Support Assistant"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Welcome Message</label>
                  <textarea
                    className="w-full mt-1 px-3 py-2 border rounded-md"
                    rows={3}
                    defaultValue="Hi there! 👋 How can I help you today?"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Primary Color</label>
                  <div className="flex items-center mt-1 gap-2">
                    <input
                      type="color"
                      className="w-10 h-10 rounded cursor-pointer"
                      defaultValue="#7C3AED"
                    />
                    <input
                      type="text"
                      className="flex-1 px-3 py-2 border rounded-md"
                      defaultValue="#7C3AED"
                    />
                  </div>
                </div>
                <Button className="w-full">Save Changes</Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Installation</CardTitle>
              <CardDescription>
                Add this code to your website to display the chatbot widget.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-muted p-4 rounded-md">
                <code className="text-xs break-all">
                  {`<script src="https://chatbot-widget.example.com/widget.js" data-widget-id="YOUR_WIDGET_ID" async></script>`}
                </code>
              </div>
              <Button variant="outline" className="w-full mt-4">
                Copy Code
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Home;

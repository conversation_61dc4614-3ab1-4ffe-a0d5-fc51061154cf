{"config": {"configFile": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\playwright.config.ts", "rootDir": "D:/VSCode Projects/Webton Projects/Webton-Chatbots/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\global-setup.ts", "globalTeardown": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 8}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "D:/VSCode Projects/Webton Projects/Webton-Chatbots/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "chromium", "name": "chromium", "testDir": "D:/VSCode Projects/Webton Projects/Webton-Chatbots/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/VSCode Projects/Webton Projects/Webton-Chatbots/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "firefox", "name": "firefox", "testDir": "D:/VSCode Projects/Webton Projects/Webton-Chatbots/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/VSCode Projects/Webton Projects/Webton-Chatbots/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "webkit", "name": "webkit", "testDir": "D:/VSCode Projects/Webton Projects/Webton-Chatbots/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/VSCode Projects/Webton Projects/Webton-Chatbots/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "D:/VSCode Projects/Webton Projects/Webton-Chatbots/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/VSCode Projects/Webton Projects/Webton-Chatbots/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "D:/VSCode Projects/Webton Projects/Webton-Chatbots/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.2", "workers": 8, "webServer": {"command": "npm run dev", "url": "http://localhost:5173", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "basic.spec.ts", "file": "basic.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Basic E2E Tests", "file": "basic.spec.ts", "line": 9, "column": 6, "specs": [{"title": "should load a basic page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 453, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T22:32:24.778Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "8946741c555a0c4515d8-f37fa2e8477b0c602256", "file": "basic.spec.ts", "line": 10, "column": 3}, {"title": "should handle form interactions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 476, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T22:32:24.795Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "8946741c555a0c4515d8-cf81022ebc8cba69dda8", "file": "basic.spec.ts", "line": 42, "column": 3}, {"title": "should support API mocking", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 5802, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('#api-result')\nExpected string: \u001b[32m\"Mocked response\"\u001b[39m\nReceived string: \u001b[31m\"Error: Failed to execute 'fetch' on 'Window': Failed to parse URL from /api/test\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('#api-result')\u001b[22m\n\u001b[2m    9 × locator resolved to <div id=\"api-result\">Error: Failed to execute 'fetch' on 'Window': Fai…</div>\u001b[22m\n\u001b[2m      - unexpected value \"Error: Failed to execute 'fetch' on 'Window': Failed to parse URL from /api/test\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('#api-result')\nExpected string: \u001b[32m\"Mocked response\"\u001b[39m\nReceived string: \u001b[31m\"Error: Failed to execute 'fetch' on 'Window': Failed to parse URL from /api/test\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('#api-result')\u001b[22m\n\u001b[2m    9 × locator resolved to <div id=\"api-result\">Error: Failed to execute 'fetch' on 'Window': Fai…</div>\u001b[22m\n\u001b[2m      - unexpected value \"Error: Failed to execute 'fetch' on 'Window': Failed to parse URL from /api/test\"\u001b[22m\n\n    at D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts:111:47", "location": {"file": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts", "column": 47, "line": 111}, "snippet": "\u001b[0m \u001b[90m 109 |\u001b[39m\n \u001b[90m 110 |\u001b[39m     \u001b[90m// Verify mocked response\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 111 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#api-result'\u001b[39m))\u001b[33m.\u001b[39mtoHaveText(\u001b[32m'Mocked response'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 112 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 113 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 114 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts", "column": 47, "line": 111}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('#api-result')\nExpected string: \u001b[32m\"Mocked response\"\u001b[39m\nReceived string: \u001b[31m\"Error: Failed to execute 'fetch' on 'Window': Failed to parse URL from /api/test\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('#api-result')\u001b[22m\n\u001b[2m    9 × locator resolved to <div id=\"api-result\">Error: Failed to execute 'fetch' on 'Window': Fai…</div>\u001b[22m\n\u001b[2m      - unexpected value \"Error: Failed to execute 'fetch' on 'Window': Failed to parse URL from /api/test\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 109 |\u001b[39m\n \u001b[90m 110 |\u001b[39m     \u001b[90m// Verify mocked response\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 111 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#api-result'\u001b[39m))\u001b[33m.\u001b[39mtoHaveText(\u001b[32m'Mocked response'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 112 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 113 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 114 |\u001b[39m\u001b[0m\n\u001b[2m    at D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts:111:47\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T22:32:24.779Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\test-results\\basic-Basic-E2E-Tests-should-support-API-mocking-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\test-results\\basic-Basic-E2E-Tests-should-support-API-mocking-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\test-results\\basic-Basic-E2E-Tests-should-support-API-mocking-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts", "column": 47, "line": 111}}], "status": "unexpected"}], "id": "8946741c555a0c4515d8-663454be33ea24ff1fcc", "file": "basic.spec.ts", "line": 76, "column": 3}, {"title": "should load a basic page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 1960, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T22:32:24.811Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "8946741c555a0c4515d8-29a066277f04e486a0f3", "file": "basic.spec.ts", "line": 10, "column": 3}, {"title": "should handle form interactions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 1790, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T22:32:24.827Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "8946741c555a0c4515d8-b3a7bddabc820a491730", "file": "basic.spec.ts", "line": 42, "column": 3}, {"title": "should support API mocking", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "failed", "duration": 6758, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('#api-result')\nExpected string: \u001b[32m\"Mocked response\"\u001b[39m\nReceived string: \u001b[31m\"Error: Window.fetch: /api/test is not a valid URL.\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('#api-result')\u001b[22m\n\u001b[2m    9 × locator resolved to <div id=\"api-result\">Error: Window.fetch: /api/test is not a valid URL.</div>\u001b[22m\n\u001b[2m      - unexpected value \"Error: Window.fetch: /api/test is not a valid URL.\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('#api-result')\nExpected string: \u001b[32m\"Mocked response\"\u001b[39m\nReceived string: \u001b[31m\"Error: Window.fetch: /api/test is not a valid URL.\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('#api-result')\u001b[22m\n\u001b[2m    9 × locator resolved to <div id=\"api-result\">Error: Window.fetch: /api/test is not a valid URL.</div>\u001b[22m\n\u001b[2m      - unexpected value \"Error: Window.fetch: /api/test is not a valid URL.\"\u001b[22m\n\n    at D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts:111:47", "location": {"file": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts", "column": 47, "line": 111}, "snippet": "\u001b[0m \u001b[90m 109 |\u001b[39m\n \u001b[90m 110 |\u001b[39m     \u001b[90m// Verify mocked response\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 111 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#api-result'\u001b[39m))\u001b[33m.\u001b[39mtoHaveText(\u001b[32m'Mocked response'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 112 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 113 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 114 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts", "column": 47, "line": 111}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('#api-result')\nExpected string: \u001b[32m\"Mocked response\"\u001b[39m\nReceived string: \u001b[31m\"Error: Window.fetch: /api/test is not a valid URL.\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('#api-result')\u001b[22m\n\u001b[2m    9 × locator resolved to <div id=\"api-result\">Error: Window.fetch: /api/test is not a valid URL.</div>\u001b[22m\n\u001b[2m      - unexpected value \"Error: Window.fetch: /api/test is not a valid URL.\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 109 |\u001b[39m\n \u001b[90m 110 |\u001b[39m     \u001b[90m// Verify mocked response\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 111 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#api-result'\u001b[39m))\u001b[33m.\u001b[39mtoHaveText(\u001b[32m'Mocked response'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 112 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 113 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 114 |\u001b[39m\u001b[0m\n\u001b[2m    at D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts:111:47\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T22:32:25.055Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\test-results\\basic-Basic-E2E-Tests-should-support-API-mocking-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\test-results\\basic-Basic-E2E-Tests-should-support-API-mocking-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\test-results\\basic-Basic-E2E-Tests-should-support-API-mocking-firefox\\error-context.md"}], "errorLocation": {"file": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts", "column": 47, "line": 111}}], "status": "unexpected"}], "id": "8946741c555a0c4515d8-23dbec90635ec2fd6854", "file": "basic.spec.ts", "line": 76, "column": 3}, {"title": "should load a basic page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "passed", "duration": 1212, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T22:32:25.073Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "8946741c555a0c4515d8-ff38ab68ba02640b181f", "file": "basic.spec.ts", "line": 10, "column": 3}, {"title": "should handle form interactions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "passed", "duration": 1303, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T22:32:25.056Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "8946741c555a0c4515d8-f44c4d5f83e04735781c", "file": "basic.spec.ts", "line": 42, "column": 3}, {"title": "should support API mocking", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 8, "parallelIndex": 0, "status": "failed", "duration": 5832, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('#api-result')\nExpected string: \u001b[32m\"Mocked response\"\u001b[39m\nReceived string: \u001b[31m\"Error: URL is not valid or contains user credentials.\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('#api-result')\u001b[22m\n\u001b[2m    9 × locator resolved to <div id=\"api-result\">Error: URL is not valid or contains user credenti…</div>\u001b[22m\n\u001b[2m      - unexpected value \"Error: URL is not valid or contains user credentials.\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('#api-result')\nExpected string: \u001b[32m\"Mocked response\"\u001b[39m\nReceived string: \u001b[31m\"Error: URL is not valid or contains user credentials.\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('#api-result')\u001b[22m\n\u001b[2m    9 × locator resolved to <div id=\"api-result\">Error: URL is not valid or contains user credenti…</div>\u001b[22m\n\u001b[2m      - unexpected value \"Error: URL is not valid or contains user credentials.\"\u001b[22m\n\n    at D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts:111:47", "location": {"file": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts", "column": 47, "line": 111}, "snippet": "\u001b[0m \u001b[90m 109 |\u001b[39m\n \u001b[90m 110 |\u001b[39m     \u001b[90m// Verify mocked response\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 111 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#api-result'\u001b[39m))\u001b[33m.\u001b[39mtoHaveText(\u001b[32m'Mocked response'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 112 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 113 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 114 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts", "column": 47, "line": 111}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('#api-result')\nExpected string: \u001b[32m\"Mocked response\"\u001b[39m\nReceived string: \u001b[31m\"Error: URL is not valid or contains user credentials.\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('#api-result')\u001b[22m\n\u001b[2m    9 × locator resolved to <div id=\"api-result\">Error: URL is not valid or contains user credenti…</div>\u001b[22m\n\u001b[2m      - unexpected value \"Error: URL is not valid or contains user credentials.\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 109 |\u001b[39m\n \u001b[90m 110 |\u001b[39m     \u001b[90m// Verify mocked response\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 111 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#api-result'\u001b[39m))\u001b[33m.\u001b[39mtoHaveText(\u001b[32m'Mocked response'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 112 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 113 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 114 |\u001b[39m\u001b[0m\n\u001b[2m    at D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts:111:47\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T22:32:26.593Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\test-results\\basic-Basic-E2E-Tests-should-support-API-mocking-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\test-results\\basic-Basic-E2E-Tests-should-support-API-mocking-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\test-results\\basic-Basic-E2E-Tests-should-support-API-mocking-webkit\\error-context.md"}], "errorLocation": {"file": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts", "column": 47, "line": 111}}], "status": "unexpected"}], "id": "8946741c555a0c4515d8-b8262f5886f88ae49cb2", "file": "basic.spec.ts", "line": 76, "column": 3}, {"title": "should load a basic page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 9, "parallelIndex": 1, "status": "passed", "duration": 434, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T22:32:26.592Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "8946741c555a0c4515d8-d5e08ffc5e526603bbac", "file": "basic.spec.ts", "line": 10, "column": 3}, {"title": "should handle form interactions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 10, "parallelIndex": 6, "status": "passed", "duration": 576, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T22:32:28.343Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "8946741c555a0c4515d8-e660e3195dd80394d33f", "file": "basic.spec.ts", "line": 42, "column": 3}, {"title": "should support API mocking", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 11, "parallelIndex": 7, "status": "failed", "duration": 5719, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('#api-result')\nExpected string: \u001b[32m\"Mocked response\"\u001b[39m\nReceived string: \u001b[31m\"Error: Failed to execute 'fetch' on 'Window': Failed to parse URL from /api/test\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('#api-result')\u001b[22m\n\u001b[2m    9 × locator resolved to <div id=\"api-result\">Error: Failed to execute 'fetch' on 'Window': Fai…</div>\u001b[22m\n\u001b[2m      - unexpected value \"Error: Failed to execute 'fetch' on 'Window': Failed to parse URL from /api/test\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('#api-result')\nExpected string: \u001b[32m\"Mocked response\"\u001b[39m\nReceived string: \u001b[31m\"Error: Failed to execute 'fetch' on 'Window': Failed to parse URL from /api/test\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('#api-result')\u001b[22m\n\u001b[2m    9 × locator resolved to <div id=\"api-result\">Error: Failed to execute 'fetch' on 'Window': Fai…</div>\u001b[22m\n\u001b[2m      - unexpected value \"Error: Failed to execute 'fetch' on 'Window': Failed to parse URL from /api/test\"\u001b[22m\n\n    at D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts:111:47", "location": {"file": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts", "column": 47, "line": 111}, "snippet": "\u001b[0m \u001b[90m 109 |\u001b[39m\n \u001b[90m 110 |\u001b[39m     \u001b[90m// Verify mocked response\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 111 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#api-result'\u001b[39m))\u001b[33m.\u001b[39mtoHaveText(\u001b[32m'Mocked response'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 112 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 113 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 114 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts", "column": 47, "line": 111}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('#api-result')\nExpected string: \u001b[32m\"Mocked response\"\u001b[39m\nReceived string: \u001b[31m\"Error: Failed to execute 'fetch' on 'Window': Failed to parse URL from /api/test\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('#api-result')\u001b[22m\n\u001b[2m    9 × locator resolved to <div id=\"api-result\">Error: Failed to execute 'fetch' on 'Window': Fai…</div>\u001b[22m\n\u001b[2m      - unexpected value \"Error: Failed to execute 'fetch' on 'Window': Failed to parse URL from /api/test\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 109 |\u001b[39m\n \u001b[90m 110 |\u001b[39m     \u001b[90m// Verify mocked response\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 111 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#api-result'\u001b[39m))\u001b[33m.\u001b[39mtoHaveText(\u001b[32m'Mocked response'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 112 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 113 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 114 |\u001b[39m\u001b[0m\n\u001b[2m    at D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts:111:47\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T22:32:28.400Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\test-results\\basic-Basic-E2E-Tests-should-support-API-mocking-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\test-results\\basic-Basic-E2E-Tests-should-support-API-mocking-Mobile-Chrome\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\test-results\\basic-Basic-E2E-Tests-should-support-API-mocking-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts", "column": 47, "line": 111}}], "status": "unexpected"}], "id": "8946741c555a0c4515d8-ecbadf90530f1f91ac7d", "file": "basic.spec.ts", "line": 76, "column": 3}, {"title": "should load a basic page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 12, "parallelIndex": 1, "status": "passed", "duration": 834, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T22:32:28.544Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "8946741c555a0c4515d8-34d19fabb707eed5c225", "file": "basic.spec.ts", "line": 10, "column": 3}, {"title": "should handle form interactions", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 13, "parallelIndex": 6, "status": "passed", "duration": 672, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T22:32:30.423Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "8946741c555a0c4515d8-d5fdeaeea897f64437dd", "file": "basic.spec.ts", "line": 42, "column": 3}, {"title": "should support API mocking", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 12, "parallelIndex": 1, "status": "failed", "duration": 5593, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('#api-result')\nExpected string: \u001b[32m\"Mocked response\"\u001b[39m\nReceived string: \u001b[31m\"Error: URL is not valid or contains user credentials.\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('#api-result')\u001b[22m\n\u001b[2m    9 × locator resolved to <div id=\"api-result\">Error: URL is not valid or contains user credenti…</div>\u001b[22m\n\u001b[2m      - unexpected value \"Error: URL is not valid or contains user credentials.\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('#api-result')\nExpected string: \u001b[32m\"Mocked response\"\u001b[39m\nReceived string: \u001b[31m\"Error: URL is not valid or contains user credentials.\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('#api-result')\u001b[22m\n\u001b[2m    9 × locator resolved to <div id=\"api-result\">Error: URL is not valid or contains user credenti…</div>\u001b[22m\n\u001b[2m      - unexpected value \"Error: URL is not valid or contains user credentials.\"\u001b[22m\n\n    at D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts:111:47", "location": {"file": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts", "column": 47, "line": 111}, "snippet": "\u001b[0m \u001b[90m 109 |\u001b[39m\n \u001b[90m 110 |\u001b[39m     \u001b[90m// Verify mocked response\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 111 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#api-result'\u001b[39m))\u001b[33m.\u001b[39mtoHaveText(\u001b[32m'Mocked response'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 112 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 113 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 114 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts", "column": 47, "line": 111}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('#api-result')\nExpected string: \u001b[32m\"Mocked response\"\u001b[39m\nReceived string: \u001b[31m\"Error: URL is not valid or contains user credentials.\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('#api-result')\u001b[22m\n\u001b[2m    9 × locator resolved to <div id=\"api-result\">Error: URL is not valid or contains user credenti…</div>\u001b[22m\n\u001b[2m      - unexpected value \"Error: URL is not valid or contains user credentials.\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 109 |\u001b[39m\n \u001b[90m 110 |\u001b[39m     \u001b[90m// Verify mocked response\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 111 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#api-result'\u001b[39m))\u001b[33m.\u001b[39mtoHaveText(\u001b[32m'Mocked response'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 112 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 113 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 114 |\u001b[39m\u001b[0m\n\u001b[2m    at D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts:111:47\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-03T22:32:29.547Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\test-results\\basic-Basic-E2E-Tests-should-support-API-mocking-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\test-results\\basic-Basic-E2E-Tests-should-support-API-mocking-Mobile-Safari\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\test-results\\basic-Basic-E2E-Tests-should-support-API-mocking-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "D:\\VSCode Projects\\Webton Projects\\Webton-Chatbots\\tests\\e2e\\basic.spec.ts", "column": 47, "line": 111}}], "status": "unexpected"}], "id": "8946741c555a0c4515d8-e30d27f2207e04843698", "file": "basic.spec.ts", "line": 76, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-07-03T22:32:21.608Z", "duration": 13770.253, "expected": 10, "skipped": 0, "unexpected": 5, "flaky": 0}}
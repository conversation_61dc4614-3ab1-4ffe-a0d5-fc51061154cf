/**
 * AnalyticsDashboard Component Unit Tests
 * 
 * Tests for analytics dashboard functionality, charts, and data visualization.
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import AnalyticsDashboard from '../../../../src/components/Dashboard/AnalyticsDashboard';
import { renderWithProviders, mockAPIResponses } from '../../../src/test/utils';

// Mock Chart.js
vi.mock('react-chartjs-2', () => ({
  Line: ({ data, options }: any) => (
    <div data-testid="line-chart" data-chart-data={JSON.stringify(data)}>
      Line Chart
    </div>
  ),
  Bar: ({ data, options }: any) => (
    <div data-testid="bar-chart" data-chart-data={JSON.stringify(data)}>
      Bar Chart
    </div>
  ),
  Doughnut: ({ data, options }: any) => (
    <div data-testid="doughnut-chart" data-chart-data={JSON.stringify(data)}>
      Doughnut Chart
    </div>
  ),
}));

vi.mock('chart.js', () => ({
  Chart: {
    register: vi.fn(),
  },
  CategoryScale: vi.fn(),
  LinearScale: vi.fn(),
  PointElement: vi.fn(),
  LineElement: vi.fn(),
  BarElement: vi.fn(),
  ArcElement: vi.fn(),
  Title: vi.fn(),
  Tooltip: vi.fn(),
  Legend: vi.fn(),
}));

// Mock API service
const mockGetAnalytics = vi.fn();
vi.mock('../../../../src/services/api', () => ({
  WebtonAPIClient: vi.fn(() => ({
    getAnalytics: mockGetAnalytics,
  })),
}));

describe('AnalyticsDashboard', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mock response
    mockGetAnalytics.mockResolvedValue(mockAPIResponses.analytics);
  });

  it('should render analytics dashboard with loading state', () => {
    // Mock loading state
    mockGetAnalytics.mockImplementation(() => new Promise(() => {}));

    renderWithProviders(<AnalyticsDashboard />);

    expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument();
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('should display key metrics cards', async () => {
    renderWithProviders(<AnalyticsDashboard />);

    await waitFor(() => {
      expect(screen.getByText('Total Conversations')).toBeInTheDocument();
      expect(screen.getByText('150')).toBeInTheDocument();
      
      expect(screen.getByText('Total Messages')).toBeInTheDocument();
      expect(screen.getByText('1,250')).toBeInTheDocument();
      
      expect(screen.getByText('Avg Response Time')).toBeInTheDocument();
      expect(screen.getByText('2.5s')).toBeInTheDocument();
      
      expect(screen.getByText('User Satisfaction')).toBeInTheDocument();
      expect(screen.getByText('4.2/5')).toBeInTheDocument();
    });
  });

  it('should render conversation trends chart', async () => {
    renderWithProviders(<AnalyticsDashboard />);

    await waitFor(() => {
      expect(screen.getByTestId('line-chart')).toBeInTheDocument();
      expect(screen.getByText('Conversation Trends')).toBeInTheDocument();
    });

    const chartElement = screen.getByTestId('line-chart');
    const chartData = JSON.parse(chartElement.getAttribute('data-chart-data') || '{}');
    
    expect(chartData.labels).toEqual(['2023-01-01', '2023-01-02', '2023-01-03']);
    expect(chartData.datasets[0].data).toEqual([10, 15, 12]);
  });

  it('should render message distribution chart', async () => {
    renderWithProviders(<AnalyticsDashboard />);

    await waitFor(() => {
      expect(screen.getByTestId('bar-chart')).toBeInTheDocument();
      expect(screen.getByText('Messages by Hour')).toBeInTheDocument();
    });

    const chartElement = screen.getByTestId('bar-chart');
    const chartData = JSON.parse(chartElement.getAttribute('data-chart-data') || '{}');
    
    expect(chartData.labels).toEqual(['9:00', '10:00', '11:00']);
    expect(chartData.datasets[0].data).toEqual([25, 30, 35]);
  });

  it('should handle date range selection', async () => {
    const user = userEvent.setup();
    renderWithProviders(<AnalyticsDashboard />);

    await waitFor(() => {
      expect(screen.getByTestId('date-range-selector')).toBeInTheDocument();
    });

    // Select different date range
    const dateSelector = screen.getByTestId('date-range-selector');
    await user.selectOptions(dateSelector, '30d');

    await waitFor(() => {
      expect(mockGetAnalytics).toHaveBeenCalledWith({
        dateRange: '30d',
      });
    });
  });

  it('should export analytics data', async () => {
    const user = userEvent.setup();
    
    // Mock export function
    const mockExportData = vi.fn().mockResolvedValue({
      success: true,
      downloadUrl: 'https://example.com/export.csv',
    });

    vi.mocked(require('../../../../src/services/api').WebtonAPIClient).mockImplementation(() => ({
      getAnalytics: mockGetAnalytics,
      exportAnalytics: mockExportData,
    }));

    renderWithProviders(<AnalyticsDashboard />);

    await waitFor(() => {
      expect(screen.getByText('Export Data')).toBeInTheDocument();
    });

    const exportButton = screen.getByText('Export Data');
    await user.click(exportButton);

    await waitFor(() => {
      expect(mockExportData).toHaveBeenCalled();
    });
  });

  it('should handle API errors gracefully', async () => {
    mockGetAnalytics.mockRejectedValue(new Error('API Error'));

    renderWithProviders(<AnalyticsDashboard />);

    await waitFor(() => {
      expect(screen.getByText('Failed to load analytics data')).toBeInTheDocument();
      expect(screen.getByText('Try Again')).toBeInTheDocument();
    });
  });

  it('should refresh data when refresh button is clicked', async () => {
    const user = userEvent.setup();
    renderWithProviders(<AnalyticsDashboard />);

    await waitFor(() => {
      expect(screen.getByTestId('refresh-button')).toBeInTheDocument();
    });

    const refreshButton = screen.getByTestId('refresh-button');
    await user.click(refreshButton);

    await waitFor(() => {
      expect(mockGetAnalytics).toHaveBeenCalledTimes(2);
    });
  });

  it('should display empty state when no data available', async () => {
    mockGetAnalytics.mockResolvedValue({
      success: true,
      data: {
        totalConversations: 0,
        totalMessages: 0,
        averageResponseTime: 0,
        userSatisfaction: 0,
        conversationsByDay: [],
        messagesByHour: [],
      },
    });

    renderWithProviders(<AnalyticsDashboard />);

    await waitFor(() => {
      expect(screen.getByText('No data available')).toBeInTheDocument();
      expect(screen.getByText('Start conversations to see analytics')).toBeInTheDocument();
    });
  });

  it('should format numbers correctly', async () => {
    mockGetAnalytics.mockResolvedValue({
      success: true,
      data: {
        totalConversations: 1234,
        totalMessages: 12345,
        averageResponseTime: 2.567,
        userSatisfaction: 4.23456,
        conversationsByDay: [],
        messagesByHour: [],
      },
    });

    renderWithProviders(<AnalyticsDashboard />);

    await waitFor(() => {
      expect(screen.getByText('1,234')).toBeInTheDocument();
      expect(screen.getByText('12,345')).toBeInTheDocument();
      expect(screen.getByText('2.6s')).toBeInTheDocument();
      expect(screen.getByText('4.2/5')).toBeInTheDocument();
    });
  });

  it('should be responsive on different screen sizes', async () => {
    // Mock window.matchMedia for responsive testing
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation(query => ({
        matches: query.includes('768px'),
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      })),
    });

    renderWithProviders(<AnalyticsDashboard />);

    await waitFor(() => {
      expect(screen.getByTestId('analytics-grid')).toHaveClass('responsive-grid');
    });
  });

  it('should show real-time updates when enabled', async () => {
    const user = userEvent.setup();
    renderWithProviders(<AnalyticsDashboard />);

    await waitFor(() => {
      expect(screen.getByTestId('real-time-toggle')).toBeInTheDocument();
    });

    const realTimeToggle = screen.getByTestId('real-time-toggle');
    await user.click(realTimeToggle);

    // Should start polling for updates
    await waitFor(() => {
      expect(screen.getByText('Real-time updates enabled')).toBeInTheDocument();
    });

    // Verify polling behavior
    await new Promise(resolve => setTimeout(resolve, 5100)); // Wait for polling interval
    
    expect(mockGetAnalytics).toHaveBeenCalledTimes(2);
  });

  it('should filter data by bot when bot filter is applied', async () => {
    const user = userEvent.setup();
    renderWithProviders(<AnalyticsDashboard />);

    await waitFor(() => {
      expect(screen.getByTestId('bot-filter')).toBeInTheDocument();
    });

    const botFilter = screen.getByTestId('bot-filter');
    await user.selectOptions(botFilter, 'bot-123');

    await waitFor(() => {
      expect(mockGetAnalytics).toHaveBeenCalledWith({
        botId: 'bot-123',
      });
    });
  });
});

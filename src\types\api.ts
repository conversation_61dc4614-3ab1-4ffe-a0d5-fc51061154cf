/**
 * API Types and Interfaces
 * 
 * Comprehensive TypeScript definitions for the Webton AI Chatbots Platform API.
 * These types ensure type safety across the frontend-backend integration.
 */

// ============================================================================
// Core Entity Types
// ============================================================================

export interface User {
  id: string;
  email: string;
  full_name: string;
  company_name?: string;
  plan_type: 'free' | 'pro' | 'enterprise';
  api_key?: string;
  created_at: string;
  updated_at: string;
}

export interface Bot {
  id: string;
  user_id: string;
  name: string;
  type: 'faq' | 'lead_generation';
  status: 'active' | 'inactive' | 'training';
  config: BotConfig;
  knowledge_base_id?: string;
  created_at: string;
  updated_at: string;
}

export interface BotConfig {
  company_name: string;
  primary_color: string;
  secondary_color: string;
  greeting_message: string;
  position: 'bottom-right' | 'bottom-left';
  logo?: string;
  fallback_message?: string;
  escalation_enabled?: boolean;
  max_response_time?: number;
  confidence_threshold?: number;
}

export interface Message {
  id: string;
  conversation_id: string;
  content: string;
  sender: 'user' | 'bot' | 'system';
  message_type: 'text' | 'image' | 'file' | 'system';
  metadata?: Record<string, any>;
  ai_response_time?: number;
  feedback_score?: number;
  created_at: string;
}

export interface Conversation {
  id: string;
  bot_id: string;
  session_id: string;
  user_identifier?: string;
  status: 'active' | 'ended' | 'escalated';
  metadata?: Record<string, any>;
  started_at: string;
  ended_at?: string;
  message_count?: number;
  last_message?: Message;
}

export interface KnowledgeDocument {
  id: string;
  bot_id: string;
  title: string;
  content?: string;
  file_path?: string;
  file_type?: string;
  file_size?: number;
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
  embedding_status: 'pending' | 'processing' | 'completed' | 'failed';
  created_at: string;
  updated_at: string;
}

// ============================================================================
// API Request/Response Types
// ============================================================================

export interface PaginationInfo {
  page: number;
  per_page: number;
  total: number;
  total_pages: number;
}

export interface APIResponse<T> {
  data: T;
  pagination?: PaginationInfo;
  meta?: Record<string, any>;
}

export interface APIError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// Authentication
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

export interface RegisterRequest {
  email: string;
  password: string;
  full_name: string;
  company_name?: string;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

// Bot Management
export interface CreateBotRequest {
  name: string;
  type: 'faq' | 'lead_generation';
  config: BotConfig;
}

export interface UpdateBotRequest {
  name?: string;
  config?: Partial<BotConfig>;
  status?: 'active' | 'inactive';
}

export interface GetBotsRequest {
  page?: number;
  per_page?: number;
  status?: string;
  type?: string;
}

// Conversations
export interface SendMessageRequest {
  message: string;
  session_id: string;
  user_id?: string;
  metadata?: Record<string, any>;
}

export interface SendMessageResponse {
  response: string;
  message_id: string;
  session_id: string;
  response_time: number;
  confidence_score?: number;
  sources?: DocumentSource[];
}

export interface DocumentSource {
  document_id: string;
  title: string;
  relevance_score: number;
  excerpt: string;
}

export interface GetConversationsRequest {
  page?: number;
  per_page?: number;
  status?: string;
  start_date?: string;
  end_date?: string;
}

// Knowledge Base
export interface UploadDocumentRequest {
  file: File;
  title?: string;
  description?: string;
}

export interface UploadDocumentResponse {
  document_id: string;
  title: string;
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
  estimated_processing_time: number;
}

export interface GetDocumentsRequest {
  page?: number;
  per_page?: number;
  processing_status?: string;
}

// Analytics
export interface GetAnalyticsRequest {
  start_date: string;
  end_date: string;
  metrics?: string[];
  granularity?: 'hour' | 'day' | 'week' | 'month';
}

export interface AnalyticsMetrics {
  total_conversations: number;
  total_messages: number;
  unique_users: number;
  average_response_time: number;
  satisfaction_score: number;
  resolution_rate: number;
}

export interface TimeSeriesData {
  timestamp: string;
  conversations: number;
  messages: number;
  response_time: number;
  satisfaction_score?: number;
}

export interface GetAnalyticsResponse {
  period: {
    start_date: string;
    end_date: string;
  };
  metrics: AnalyticsMetrics;
  time_series?: TimeSeriesData[];
}

// ============================================================================
// WebSocket Types
// ============================================================================

export interface WebSocketMessage<T = any> {
  type: string;
  payload: T;
  timestamp: string;
}

export interface JoinConversationPayload {
  session_id: string;
  bot_id: string;
}

export interface SendMessagePayload {
  message: string;
  session_id: string;
}

export interface TypingPayload {
  session_id: string;
}

export interface MessageReceivedPayload {
  message: Message;
  session_id: string;
}

export interface ConversationEndedPayload {
  session_id: string;
  reason: string;
}

// ============================================================================
// Widget Integration Types
// ============================================================================

export interface WidgetConfig {
  bot_id: string;
  company_name: string;
  primary_color: string;
  secondary_color?: string;
  position: 'bottom-right' | 'bottom-left';
  greeting_message?: string;
  logo?: string;
  theme?: 'light' | 'dark' | 'auto';
}

export interface WidgetInitOptions {
  config: WidgetConfig;
  api_endpoint?: string;
  websocket_endpoint?: string;
  debug?: boolean;
}

// ============================================================================
// Utility Types
// ============================================================================

export type APIMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

export interface RequestConfig {
  method: APIMethod;
  url: string;
  data?: any;
  params?: Record<string, any>;
  headers?: Record<string, string>;
}

export interface APIClientConfig {
  baseURL: string;
  apiKey?: string;
  accessToken?: string;
  timeout?: number;
  retries?: number;
}

// ============================================================================
// Error Types
// ============================================================================

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface APIErrorResponse {
  error: {
    code: string;
    message: string;
    details?: Record<string, any>;
    validation_errors?: ValidationError[];
  };
}

// ============================================================================
// Event Types
// ============================================================================

export interface AnalyticsEvent {
  id: string;
  bot_id: string;
  event_type: 'conversation_started' | 'conversation_ended' | 'message_sent' | 'message_received' | 'lead_captured' | 'feedback_given';
  event_data: Record<string, any>;
  user_identifier?: string;
  session_id?: string;
  created_at: string;
}

export interface WebhookEvent {
  event: string;
  timestamp: string;
  data: Record<string, any>;
}

// ============================================================================
// Rate Limiting Types
// ============================================================================

export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: number;
  window: number;
}

export interface RateLimitHeaders {
  'X-RateLimit-Limit': string;
  'X-RateLimit-Remaining': string;
  'X-RateLimit-Reset': string;
  'X-RateLimit-Window': string;
}

/**
 * Vite Configuration for Standalone Widget Bundle
 * 
 * This configuration builds the chat widget as a standalone JavaScript file
 * that can be embedded on external websites via a simple script tag.
 */

import path from "path";
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";

export default defineConfig({
  // Build configuration for widget
  build: {
    // Output directory for widget build
    outDir: "dist/widget",
    
    // Clear output directory before build
    emptyOutDir: true,
    
    // Library mode for standalone widget
    lib: {
      entry: path.resolve(__dirname, "src/widget/widget-entry.ts"),
      name: "WebtonChatbot",
      fileName: "chatbot-widget",
      formats: ["iife"], // Immediately Invoked Function Expression for browser
    },
    
    // Rollup options for advanced bundling
    rollupOptions: {
      // External dependencies (none for standalone widget)
      external: [],
      
      output: {
        // Global variables for IIFE format
        globals: {},
        
        // Asset file names
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name?.split('.') || [];
          const ext = info[info.length - 1];
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext || '')) {
            return `assets/images/[name]-[hash][extname]`;
          }
          if (/css/i.test(ext || '')) {
            return `assets/css/[name]-[hash][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        },
        
        // Chunk file names
        chunkFileNames: 'assets/js/[name]-[hash].js',
        
        // Entry file names
        entryFileNames: 'chatbot-widget.js',
      },
    },
    
    // Minification
    minify: 'terser',
    
    // Source maps for debugging
    sourcemap: false,
    
    // Target modern browsers for smaller bundle
    target: 'es2015',
    
    // CSS code splitting
    cssCodeSplit: false,
  },
  
  // Plugins
  plugins: [
    react({
      // JSX runtime for smaller bundle
      jsxRuntime: 'automatic',
    }),
  ],
  
  // Resolve configuration
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  
  // Define environment variables
  define: {
    // Disable development features in production
    __DEV__: false,
    
    // API endpoints for widget
    'process.env.NODE_ENV': JSON.stringify('production'),
    'import.meta.env.VITE_API_BASE_URL': JSON.stringify(
      process.env.VITE_API_BASE_URL || 'https://api.webton-chatbots.com/v1'
    ),
    'import.meta.env.VITE_WS_URL': JSON.stringify(
      process.env.VITE_WS_URL || 'wss://api.webton-chatbots.com/ws'
    ),
  },
  
  // CSS configuration
  css: {
    // CSS modules configuration
    modules: {
      // Scoped CSS for widget isolation
      scopeBehaviour: 'local',
      generateScopedName: 'webton-[name]__[local]___[hash:base64:5]',
    },
  },
  
  // Optimization
  optimizeDeps: {
    include: ['react', 'react-dom'],
  },
  
  // Server configuration (for development)
  server: {
    port: 3001,
    cors: true,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  },
  
  // Preview configuration
  preview: {
    port: 3001,
    cors: true,
  },
});

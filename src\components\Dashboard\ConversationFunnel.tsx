/**
 * Conversation Funnel Component
 * 
 * Displays the user journey funnel from page visit to conversion.
 * Shows drop-off rates at each stage of the conversation process.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Funnel<PERSON>hart, 
  Funnel, 
  LabelList,
  ResponsiveContainer,
  Tooltip,
  Cell,
} from 'recharts';
import { 
  Users, 
  MousePointer, 
  MessageSquare, 
  CheckCircle, 
  Mail,
  TrendingDown,
  RefreshCw,
  ArrowDown,
} from 'lucide-react';
import { cn } from '@/lib/utils';

import { MockAnalyticsService } from '@/services/analytics-mock';

interface ConversationFunnelProps {
  botId: string;
  dateRange: { from: Date; to: Date };
  className?: string;
}

interface FunnelStage {
  stage: string;
  count: number;
  percentage: number;
}

/**
 * Custom tooltip for funnel chart
 */
function FunnelTooltip({ active, payload }: any) {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    
    return (
      <div className="bg-background border border-border rounded-lg shadow-lg p-3">
        <p className="font-medium text-sm mb-2">
          {data.stage}
        </p>
        <div className="space-y-1 text-sm">
          <div className="flex items-center justify-between gap-4">
            <span className="text-muted-foreground">Users:</span>
            <span className="font-medium">{data.count.toLocaleString()}</span>
          </div>
          <div className="flex items-center justify-between gap-4">
            <span className="text-muted-foreground">Conversion:</span>
            <span className="font-medium">{data.percentage}%</span>
          </div>
        </div>
      </div>
    );
  }
  return null;
}

/**
 * Stage icon component
 */
function StageIcon({ stage }: { stage: string }) {
  switch (stage) {
    case 'Page Visitors':
      return <Users className="h-5 w-5" />;
    case 'Widget Opened':
      return <MousePointer className="h-5 w-5" />;
    case 'Started Conversation':
      return <MessageSquare className="h-5 w-5" />;
    case 'Completed Conversation':
      return <CheckCircle className="h-5 w-5" />;
    case 'Provided Contact Info':
      return <Mail className="h-5 w-5" />;
    default:
      return <Users className="h-5 w-5" />;
  }
}

export function ConversationFunnel({ 
  botId, 
  dateRange,
  className 
}: ConversationFunnelProps) {
  const [funnelData, setFunnelData] = useState<FunnelStage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Color scheme for funnel stages
  const FUNNEL_COLORS = [
    '#3b82f6', // Blue
    '#10b981', // Green
    '#f59e0b', // Yellow
    '#ef4444', // Red
    '#8b5cf6', // Purple
  ];

  /**
   * Load funnel data
   */
  const loadFunnelData = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await MockAnalyticsService.getConversationFunnel(
        botId,
        dateRange.from.toISOString(),
        dateRange.to.toISOString()
      );
      setFunnelData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load funnel data');
      console.error('Failed to load funnel data:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadFunnelData();
  }, [botId, dateRange]);

  // Calculate drop-off rates between stages
  const calculateDropOffRates = () => {
    const dropOffs = [];
    for (let i = 1; i < funnelData.length; i++) {
      const current = funnelData[i];
      const previous = funnelData[i - 1];
      const dropOffRate = ((previous.count - current.count) / previous.count) * 100;
      dropOffs.push({
        fromStage: previous.stage,
        toStage: current.stage,
        dropOffRate: dropOffRate,
        usersLost: previous.count - current.count,
      });
    }
    return dropOffs;
  };

  const dropOffs = calculateDropOffRates();
  const totalVisitors = funnelData[0]?.count || 0;
  const finalConversions = funnelData[funnelData.length - 1]?.count || 0;
  const overallConversionRate = totalVisitors > 0 ? (finalConversions / totalVisitors) * 100 : 0;

  if (loading) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingDown className="h-5 w-5" />
            Conversation Funnel
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] flex items-center justify-center">
            <div className="animate-pulse text-muted-foreground">Loading funnel data...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingDown className="h-5 w-5" />
            Conversation Funnel
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] flex flex-col items-center justify-center gap-4">
            <p className="text-red-600 text-center">{error}</p>
            <Button onClick={loadFunnelData} variant="outline" size="sm">
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <TrendingDown className="h-5 w-5" />
              Conversation Funnel
            </CardTitle>
            <CardDescription>
              User journey from page visit to conversion
            </CardDescription>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={loadFunnelData}
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
        
        {/* Summary stats */}
        <div className="flex items-center gap-4 mt-4">
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              {totalVisitors.toLocaleString()} total visitors
            </Badge>
            <Badge variant="outline" className="text-xs">
              {finalConversions.toLocaleString()} conversions
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <Badge 
              variant={overallConversionRate >= 10 ? "default" : overallConversionRate >= 5 ? "secondary" : "destructive"} 
              className="text-xs"
            >
              {overallConversionRate.toFixed(1)}% overall conversion
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Funnel visualization */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Chart */}
          <div className="h-[400px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <FunnelChart>
                <Tooltip content={<FunnelTooltip />} />
                <Funnel
                  dataKey="count"
                  data={funnelData}
                  isAnimationActive
                >
                  <LabelList position="center" fill="#fff" stroke="none" />
                  {funnelData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={FUNNEL_COLORS[index % FUNNEL_COLORS.length]} />
                  ))}
                </Funnel>
              </FunnelChart>
            </ResponsiveContainer>
          </div>
          
          {/* Stage details */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium">Stage Breakdown</h4>
            {funnelData.map((stage, index) => (
              <div key={stage.stage} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <StageIcon stage={stage.stage} />
                    <span className="text-sm font-medium">{stage.stage}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">{stage.count.toLocaleString()}</div>
                    <div className="text-xs text-muted-foreground">{stage.percentage}%</div>
                  </div>
                </div>
                <Progress 
                  value={stage.percentage} 
                  className="h-2"
                  style={{ 
                    '--progress-background': FUNNEL_COLORS[index % FUNNEL_COLORS.length] 
                  } as React.CSSProperties}
                />
                
                {/* Drop-off indicator */}
                {index < funnelData.length - 1 && dropOffs[index] && (
                  <div className="flex items-center justify-center py-2">
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <ArrowDown className="h-3 w-3" />
                      <span>
                        {dropOffs[index].usersLost.toLocaleString()} users dropped off 
                        ({dropOffs[index].dropOffRate.toFixed(1)}%)
                      </span>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
        
        {/* Insights and recommendations */}
        <div className="mt-6 pt-4 border-t">
          <h4 className="text-sm font-medium mb-3">Optimization Opportunities</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {dropOffs.map((dropOff, index) => (
              <div 
                key={index}
                className={cn(
                  "p-3 rounded-lg border",
                  dropOff.dropOffRate > 50 ? "border-red-200 bg-red-50" :
                  dropOff.dropOffRate > 30 ? "border-yellow-200 bg-yellow-50" :
                  "border-green-200 bg-green-50"
                )}
              >
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">
                    {dropOff.fromStage} → {dropOff.toStage}
                  </span>
                  <Badge 
                    variant={dropOff.dropOffRate > 50 ? "destructive" : dropOff.dropOffRate > 30 ? "secondary" : "default"}
                    className="text-xs"
                  >
                    {dropOff.dropOffRate.toFixed(1)}% drop-off
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground">
                  {dropOff.dropOffRate > 50 ? "High drop-off rate - consider improving user experience" :
                   dropOff.dropOffRate > 30 ? "Moderate drop-off - room for optimization" :
                   "Good conversion rate - maintain current approach"}
                </p>
              </div>
            ))}
          </div>
        </div>
        
        {/* Summary metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 pt-4 border-t">
          <div className="text-center">
            <div className="text-2xl font-bold">
              {((funnelData[1]?.count || 0) / (funnelData[0]?.count || 1) * 100).toFixed(1)}%
            </div>
            <div className="text-xs text-muted-foreground">Widget Open Rate</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">
              {((funnelData[2]?.count || 0) / (funnelData[1]?.count || 1) * 100).toFixed(1)}%
            </div>
            <div className="text-xs text-muted-foreground">Engagement Rate</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">
              {((funnelData[3]?.count || 0) / (funnelData[2]?.count || 1) * 100).toFixed(1)}%
            </div>
            <div className="text-xs text-muted-foreground">Completion Rate</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">
              {((funnelData[4]?.count || 0) / (funnelData[3]?.count || 1) * 100).toFixed(1)}%
            </div>
            <div className="text-xs text-muted-foreground">Lead Capture Rate</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

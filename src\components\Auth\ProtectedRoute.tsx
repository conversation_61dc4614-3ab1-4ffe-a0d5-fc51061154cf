/**
 * Protected Route Component
 * 
 * Wrapper component that protects routes based on authentication and role permissions.
 */

import React, { ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { AuthPage } from './AuthPage';
import { LoadingSpinner } from '../ui/loading-spinner';
import { Alert, AlertDescription } from '../ui/alert';
import { UserRole } from '../../lib/supabase';

interface ProtectedRouteProps {
  children: ReactNode;
  requireAuth?: boolean;
  requiredRoles?: UserRole[];
  fallbackComponent?: ReactNode;
  redirectTo?: string;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = true,
  requiredRoles = [],
  fallbackComponent,
  redirectTo,
}) => {
  const { user, profile, loading, hasAnyRole } = useAuth();
  const location = useLocation();

  // Show loading spinner while auth state is being determined
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // If authentication is not required, render children
  if (!requireAuth) {
    return <>{children}</>;
  }

  // If user is not authenticated, show auth page or redirect
  if (!user) {
    if (fallbackComponent) {
      return <>{fallbackComponent}</>;
    }
    
    if (redirectTo) {
      return <Navigate to={redirectTo} state={{ from: location }} replace />;
    }
    
    // Default: show auth page
    return <AuthPage redirectTo={location.pathname} />;
  }

  // If user is authenticated but doesn't have required roles
  if (requiredRoles.length > 0 && !hasAnyRole(requiredRoles)) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <Alert variant="destructive">
            <AlertDescription>
              You don't have permission to access this page. Please contact your administrator if you believe this is an error.
            </AlertDescription>
          </Alert>
          
          <div className="mt-4 text-center">
            <button
              onClick={() => window.history.back()}
              className="text-sm text-blue-600 hover:text-blue-500"
            >
              Go back
            </button>
          </div>
        </div>
      </div>
    );
  }

  // User is authenticated and has required permissions
  return <>{children}</>;
};

// Convenience components for common protection patterns
export const AdminRoute: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ProtectedRoute requiredRoles={['admin']}>
    {children}
  </ProtectedRoute>
);

export const ClientRoute: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ProtectedRoute requiredRoles={['admin', 'client']}>
    {children}
  </ProtectedRoute>
);

export const ViewerRoute: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ProtectedRoute requiredRoles={['admin', 'client', 'viewer']}>
    {children}
  </ProtectedRoute>
);

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Webton Chatbot Widget Example</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #4f46e5;
        }
        
        .section h2 {
            margin-top: 0;
            color: #4f46e5;
        }
        
        code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 14px;
        }
        
        pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .controls {
            margin: 20px 0;
        }
        
        button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        button:hover {
            background: #4338ca;
        }
        
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-size: 14px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .events {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .event {
            margin-bottom: 5px;
            padding: 5px;
            background: white;
            border-radius: 3px;
        }
        
        .event-time {
            color: #6c757d;
            font-size: 11px;
        }
        
        .event-type {
            font-weight: bold;
            color: #4f46e5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Webton Chatbot Widget Example</h1>
        
        <div class="section">
            <h2>Basic Integration</h2>
            <p>The simplest way to add the Webton chatbot to your website:</p>
            <pre><code>&lt;script src="https://cdn.webton-chatbots.com/embed.js" 
        data-bot-id="your-bot-id"&gt;
&lt;/script&gt;</code></pre>
        </div>
        
        <div class="section">
            <h2>Advanced Configuration</h2>
            <p>Customize the widget appearance and behavior:</p>
            <pre><code>&lt;script src="https://cdn.webton-chatbots.com/embed.js" 
        data-bot-id="demo-bot-123"
        data-company-name="Your Company"
        data-bot-name="AI Assistant"
        data-primary-color="#4f46e5"
        data-secondary-color="#ffffff"
        data-position="bottom-right"
        data-greeting="Hello! How can I help you today?"
        data-theme="light"
        data-auto-open="false"
        data-enable-feedback="true"
        data-debug="true"&gt;
&lt;/script&gt;</code></pre>
        </div>
        
        <div class="section">
            <h2>Widget Controls</h2>
            <p>Use these buttons to interact with the widget programmatically:</p>
            <div class="controls">
                <button onclick="openWidget()">Open Widget</button>
                <button onclick="closeWidget()">Close Widget</button>
                <button onclick="toggleWidget()">Toggle Widget</button>
                <button onclick="sendTestMessage()">Send Test Message</button>
                <button onclick="getWidgetStatus()">Get Status</button>
                <button onclick="clearEvents()">Clear Events</button>
            </div>
        </div>
        
        <div class="section">
            <h2>Widget Status</h2>
            <div id="status" class="status info">
                Initializing widget...
            </div>
        </div>
        
        <div class="section">
            <h2>Widget Events</h2>
            <p>Real-time events from the widget:</p>
            <div id="events" class="events">
                <div class="event">
                    <span class="event-time">[Loading...]</span>
                    <span class="event-type">Waiting for widget to load...</span>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>Configuration Options</h2>
            <p>Available data attributes for customization:</p>
            <ul>
                <li><code>data-bot-id</code> - Your bot ID (required)</li>
                <li><code>data-company-name</code> - Company name displayed in header</li>
                <li><code>data-bot-name</code> - Bot name displayed in header</li>
                <li><code>data-primary-color</code> - Primary color (hex, rgb, or named)</li>
                <li><code>data-secondary-color</code> - Secondary color</li>
                <li><code>data-position</code> - Widget position (bottom-right, bottom-left)</li>
                <li><code>data-greeting</code> - Initial greeting message</li>
                <li><code>data-logo</code> - Bot avatar image URL</li>
                <li><code>data-theme</code> - Theme (light, dark, auto)</li>
                <li><code>data-auto-open</code> - Auto-open widget (true/false)</li>
                <li><code>data-show-branding</code> - Show Webton branding (true/false)</li>
                <li><code>data-enable-file-upload</code> - Enable file uploads (true/false)</li>
                <li><code>data-enable-feedback</code> - Enable feedback system (true/false)</li>
                <li><code>data-max-message-length</code> - Maximum message length</li>
                <li><code>data-api-endpoint</code> - Custom API endpoint</li>
                <li><code>data-websocket-endpoint</code> - Custom WebSocket endpoint</li>
                <li><code>data-allowed-origins</code> - Comma-separated allowed origins</li>
                <li><code>data-debug</code> - Enable debug mode (true/false)</li>
            </ul>
        </div>
    </div>

    <!-- Widget Script with Demo Configuration -->
    <script src="./embed.js" 
            data-bot-id="demo-bot-123"
            data-company-name="Webton Demo"
            data-bot-name="Demo Assistant"
            data-primary-color="#4f46e5"
            data-position="bottom-right"
            data-greeting="Hello! This is a demo of the Webton chatbot widget. How can I help you today?"
            data-theme="light"
            data-debug="true">
    </script>

    <script>
        // Widget control functions
        function openWidget() {
            if (window.WebtonEmbed && window.WebtonEmbed.getWidget()) {
                window.WebtonEmbed.getWidget().open();
            } else {
                updateStatus('Widget not loaded yet', 'error');
            }
        }

        function closeWidget() {
            if (window.WebtonEmbed && window.WebtonEmbed.getWidget()) {
                window.WebtonEmbed.getWidget().close();
            } else {
                updateStatus('Widget not loaded yet', 'error');
            }
        }

        function toggleWidget() {
            if (window.WebtonEmbed && window.WebtonEmbed.getWidget()) {
                window.WebtonEmbed.getWidget().toggle();
            } else {
                updateStatus('Widget not loaded yet', 'error');
            }
        }

        function sendTestMessage() {
            if (window.WebtonEmbed && window.WebtonEmbed.getWidget()) {
                window.WebtonEmbed.getWidget().sendMessage('This is a test message from the example page!');
            } else {
                updateStatus('Widget not loaded yet', 'error');
            }
        }

        function getWidgetStatus() {
            if (window.WebtonEmbed && window.WebtonEmbed.getWidget()) {
                const widget = window.WebtonEmbed.getWidget();
                const status = {
                    isLoaded: window.WebtonEmbed.isLoaded(),
                    isInitialized: widget.isInitialized(),
                    isOpen: widget.isOpen(),
                    sessionId: widget.getSessionId(),
                    config: widget.getConfig()
                };
                updateStatus(`Widget Status: ${JSON.stringify(status, null, 2)}`, 'info');
                console.log('Widget Status:', status);
            } else {
                updateStatus('Widget not loaded yet', 'error');
            }
        }

        function clearEvents() {
            document.getElementById('events').innerHTML = '';
        }

        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        function addEvent(type, data) {
            const eventsEl = document.getElementById('events');
            const eventEl = document.createElement('div');
            eventEl.className = 'event';
            
            const time = new Date().toLocaleTimeString();
            const dataStr = data ? JSON.stringify(data) : '';
            
            eventEl.innerHTML = `
                <span class="event-time">[${time}]</span>
                <span class="event-type">${type}</span>
                ${dataStr ? `<div style="margin-top: 5px; color: #6c757d;">${dataStr}</div>` : ''}
            `;
            
            eventsEl.appendChild(eventEl);
            eventsEl.scrollTop = eventsEl.scrollHeight;
        }

        // Listen for widget events
        document.addEventListener('webton:embed:loaded', (e) => {
            updateStatus('Widget loaded successfully!', 'success');
            addEvent('embed:loaded', e.detail);
        });

        document.addEventListener('webton:embed:error', (e) => {
            updateStatus(`Widget load error: ${e.detail.error}`, 'error');
            addEvent('embed:error', e.detail);
        });

        document.addEventListener('webton:widget:initialized', (e) => {
            addEvent('widget:initialized', e.detail);
        });

        document.addEventListener('webton:widget:opened', (e) => {
            addEvent('widget:opened', e.detail);
        });

        document.addEventListener('webton:widget:closed', (e) => {
            addEvent('widget:closed', e.detail);
        });

        document.addEventListener('webton:widget:message', (e) => {
            addEvent('widget:message', e.detail);
        });

        document.addEventListener('webton:widget:error', (e) => {
            addEvent('widget:error', e.detail);
        });

        // Initialize status
        updateStatus('Loading widget...', 'info');
    </script>
</body>
</html>

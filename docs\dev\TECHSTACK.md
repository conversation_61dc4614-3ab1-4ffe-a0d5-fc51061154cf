# Tech Stack für AI Chatbots by <PERSON><PERSON>

Hier ist ein **kompletter, praxisorientierter Tech-Stack-Vorschlag** für euer „AI Chatbots by <PERSON>ton“-Projekt. Ziel war:

* schnell entwickelbar
* wartbar & kosteneffizient
* mitwachsend (vom MVP bis skalierbarer SaaS)
* vertraut mit eurem bestehenden Stack (Vite, Firebase, React, Supabase, ggf. Python)

---

## ✅ **Gesamtkonzept**

Ein modulares System mit 3 Hauptkomponenten:

1. **Website-Widget (per Script einbettbar)**
2. **Dashboard-Webapp für Webton-Klienten**
3. **Cloud-Backend für Bot-Logik, LLM-Zugriff & Storage**

---

## 🔧 **Frontend-Stack (Client-Side)**

### 🧩 1. **Widget (Einbettbarer Chatbot)**

* **Vite + Lit / Vanilla TS** – schnelles Build-System für Custom Elements
* **Shadow DOM-kompatibles Web Component Bundle** (leichtgewichtig, konfliktfrei)
* **Kommunikation via REST oder WebSocket** zum Webton-Backend
* **Live-Theming**: Farben, Avatar, Position etc. via Props oder API-Konfiguration

### 📊 2. **Dashboard (für Kundenverwaltung & Analytics)**

* **Vite + React + TailwindCSS**
* **Firebase Auth (Email Login oder Magic Link)**
* **TanStack Query (für API State & Caching)**
* **i18n Unterstützung (DE/EN UI für größere Zielgruppe)**
* Optional: **ShadCN UI**, um UI/UX modern & wartbar zu halten

---

## 🧠 **Backend-Stack (Bot-Logik & APIs)**

### 🔁 3. **API & Routing-Logik**

* **Node.js + Express** (klassisch) **ODER** **Next.js (API Routes + SSR Dashboard)**
* **Alternative: FastAPI (Python)** → falls ihr später eigene Modelle lokal hosten möchtet
* **Firebase Cloud Functions** (für MVP) → Skalierung & Monitoring eingebaut
* Option für Wachstum: **Supabase Edge Functions** oder **Vercel Functions** (je nach Hostingwahl)

### 🗃️ 4. **Datenbanken**

* **Firebase Firestore** (einfach, für User, Bot-Settings, Logs)

  * Alternative: **Supabase Postgres + pgvector** für komplexere Queries
* **Qdrant oder Weaviate (gehostet)** – für Vektor-DB / Embeddings-Suche
* **Firebase Storage** – für Kundendokumente (PDFs, Textdateien, Bilder etc.)

---

## 🤖 **KI / LLM-Integration**

### 🔌 5. **LLM Backends (über Adapter-Schicht)**

* **OpenAI GPT-3.5/4** – Standard, einfache API, stabile Qualität
* Optional/erweiterbar:

  * **Anthropic Claude** (für Datenschutzfokus)
  * **Mistral / Ollama** – bei lokalem Modellbedarf auf eigener GPU
* Adapter-Struktur in eurer API, um LLM schnell austauschbar zu machen

### 🧠 6. **Embeddings & RAG**

* **OpenAI Embeddings API (text-embedding-3-small)**
* Langfristig optional: **sentence-transformers (local)** via Python/ONNX
* Suche in **Qdrant/Weaviate** mit `filter: { tenant_id: xxx }` → für Mandantentrennung

---

## 📈 **Analytics & Monitoring**

* **Firebase Analytics** (besonders wenn ihr sowieso Firebase nutzt)
* **Simple custom logging (Firestore + Aggregation)**
* **Sentry** für Error-Monitoring (Dashboard + Widget)
* **Plausible.io oder Umami** – DSGVO-konformes Site-Tracking (Kundenseite)

---

## 🛡️ **Security & Auth**

* **Firebase Auth** (mit Mandantenrollen: Admin, Client, Editor etc.)
* **JWT-basierte Auth-Header** fürs Widget
* **Rate Limiting** via API-Gateway (Firebase oder z. B. Upstash Ratelimit)
* **Bot-Sicherheit** (API-Key pro Bot, optional IP-Blocking)

---

## 🧪 **Dev & Tools**

* **Monorepo mit TurboRepo oder Nx** (Widget + Dashboard + API getrennt, aber gemeinsam buildbar)
* **CI/CD über GitHub Actions + Vercel/Firebase Hosting**
* **Postman / Thunder Client für API-Tests**
* **Docker Compose** (lokales Setup: Qdrant, API, ggf. Ollama)

---

## 📦 **Deployment / Infrastruktur**

| Komponente     | Plattform                      | Status                     |
| -------------- | ------------------------------ | -------------------------- |
| **Dashboard**  | Firebase Hosting oder Vercel   | serverless, instant deploy |
| **Widget CDN** | jsDelivr, Netlify, Firebase    | Snippet mit `src="..."`    |
| **API**        | Firebase Functions oder Vercel | pay-per-use, skalierbar    |
| **Vector DB**  | Qdrant Cloud (Free Tier)       | managed, DSGVO-ready       |
| **LLM**        | OpenAI / Claude (per API)      | usage-based                |
| **DB**         | Firestore (MVP) oder Supabase  | flexibel                   |
| **Storage**    | Firebase Storage               | für Wissensdokumente       |

---

## 🚀 Beispiel: Tech Stack Zusammenfassung (für Projektseite)

```yaml
project: Webton AI Chatbot Platform
type: SaaS-Tool (Multitenant)
goal: Website-Chatbots für Support, FAQ, LeadGen

frontend:
  - Widget: Vite + Lit (CDN-injectable WebComponent)
  - Dashboard: Vite + React + Tailwind (Firebase Hosting)

backend:
  - API: Node.js (Express oder Next.js), Firebase Functions
  - DB: Firestore (User, Bots, Settings, Logs)
  - VectorDB: Qdrant Cloud
  - Auth: Firebase Auth
  - LLM: OpenAI GPT-3.5 / GPT-4 API
  - Embeddings: OpenAI embedding-3-small
  - Storage: Firebase Storage (Kundendokumente)

extras:
  - Analytics: Firebase Analytics, Sentry
  - CI/CD: GitHub + Vercel/Firebase Deploy
  - Admin UI: Mandantenverwaltung, LLM-Kostenkontrolle, Feedback
```

---

## 🧭 Nächste Schritte (Empfehlung für Kickoff)

1. **Proof-of-Concept:**

   * 1 Dummy-Bot mit FAQ-Datei + OpenAI API + Qdrant-Testsetup
   * Embed-Widget als reines TS-Snippet → testen auf Webton-Demo-Seite

2. **Dashboard MVP:**

   * Auth, Bot erstellen, Chatverlauf-Übersicht + JS-Snippet ausgeben
   * Basis-Analytics (z. B. Count, Top-5 Fragen)

3. **Kostenmodell-Simulation:**

   * Tokenverbrauch, OpenAI-Nutzung, Firestore-Reads → grobe Kalkulation

4. **Optional: Landingpage für „Chatbots by Webton“**

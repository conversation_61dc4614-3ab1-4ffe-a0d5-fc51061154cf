/**
 * Supabase Client Configuration
 * 
 * Centralized Supabase client setup for authentication and database operations.
 */

import { createClient } from '@supabase/supabase-js';
import type { Database } from '../types/supabase';

// Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error(
    'Missing Supabase environment variables. Please check your .env file and ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set.'
  );
}

/**
 * Supabase client instance with TypeScript support.
 */
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    // Enable automatic token refresh
    autoRefreshToken: true,
    // Persist session in localStorage
    persistSession: true,
    // Detect session from URL on mount
    detectSessionInUrl: true,
    // Storage key for session persistence
    storageKey: 'webton-auth-token',
    // Custom storage implementation (optional)
    storage: {
      getItem: (key: string) => {
        if (typeof window !== 'undefined') {
          return window.localStorage.getItem(key);
        }
        return null;
      },
      setItem: (key: string, value: string) => {
        if (typeof window !== 'undefined') {
          window.localStorage.setItem(key, value);
        }
      },
      removeItem: (key: string) => {
        if (typeof window !== 'undefined') {
          window.localStorage.removeItem(key);
        }
      },
    },
  },
  // Global configuration
  global: {
    headers: {
      'X-Client-Info': 'webton-chatbots-dashboard',
    },
  },
});

/**
 * Helper function to get the current user session.
 */
export const getCurrentSession = async () => {
  const { data: { session }, error } = await supabase.auth.getSession();
  if (error) {
    console.error('Error getting session:', error);
    return null;
  }
  return session;
};

/**
 * Helper function to get the current user.
 */
export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser();
  if (error) {
    console.error('Error getting user:', error);
    return null;
  }
  return user;
};

/**
 * Helper function to sign out the current user.
 */
export const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  if (error) {
    console.error('Error signing out:', error);
    throw error;
  }
};

/**
 * Helper function to check if user has specific role.
 */
export const hasRole = (user: any, role: string): boolean => {
  return user?.user_metadata?.role === role || user?.app_metadata?.role === role;
};

/**
 * Helper function to check if user has any of the specified roles.
 */
export const hasAnyRole = (user: any, roles: string[]): boolean => {
  const userRole = user?.user_metadata?.role || user?.app_metadata?.role;
  return roles.includes(userRole);
};

/**
 * Role definitions for the application.
 */
export const ROLES = {
  ADMIN: 'admin',
  CLIENT: 'client', 
  VIEWER: 'viewer',
} as const;

export type UserRole = typeof ROLES[keyof typeof ROLES];

/**
 * Permission helper functions.
 */
export const permissions = {
  canManageUsers: (user: any) => hasRole(user, ROLES.ADMIN),
  canManageBots: (user: any) => hasAnyRole(user, [ROLES.ADMIN, ROLES.CLIENT]),
  canViewAnalytics: (user: any) => hasAnyRole(user, [ROLES.ADMIN, ROLES.CLIENT, ROLES.VIEWER]),
  canEditSettings: (user: any) => hasAnyRole(user, [ROLES.ADMIN, ROLES.CLIENT]),
  canExportData: (user: any) => hasAnyRole(user, [ROLES.ADMIN, ROLES.CLIENT]),
};
